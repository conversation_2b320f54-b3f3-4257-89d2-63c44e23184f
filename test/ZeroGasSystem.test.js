const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("ZeroGas System", function () {
    let zeroGasDemo;
    let relayerService;
    let owner;
    let user1;
    let user2;
    let relayer;

    beforeEach(async function () {
        [owner, user1, user2, relayer] = await ethers.getSigners();

        // 部署合约
        const ZeroGasDemo = await ethers.getContractFactory("ZeroGasDemo");
        zeroGasDemo = await ZeroGasDemo.deploy();
        await zeroGasDemo.deployed();

        const RelayerService = await ethers.getContractFactory("RelayerService");
        relayerService = await RelayerService.deploy();
        await relayerService.deployed();

        // 初始化设置
        await zeroGasDemo.depositToGasPool({ value: ethers.utils.parseEther("5") });
        await zeroGasDemo.authorizeRelayer(relayer.address, true);
    });

    describe("基础功能测试", function () {
        it("应该能够更新用户资料", async function () {
            await zeroGasDemo.connect(user1).updateProfile("Alice", 100);
            
            const profile = await zeroGasDemo.getProfile(user1.address);
            expect(profile.name).to.equal("Alice");
            expect(profile.score).to.equal(100);
            expect(profile.level).to.equal(2); // score 100 对应 level 2
        });

        it("应该能够转账代币", async function () {
            // 先给 user1 一些代币
            await zeroGasDemo.mintTokens(user1.address, ethers.utils.parseEther("100"));
            
            const initialBalance = await zeroGasDemo.balanceOf(user1.address);
            expect(initialBalance).to.equal(ethers.utils.parseEther("100"));

            // 转账
            await zeroGasDemo.connect(user1).transfer(user2.address, ethers.utils.parseEther("10"));
            
            const user1Balance = await zeroGasDemo.balanceOf(user1.address);
            const user2Balance = await zeroGasDemo.balanceOf(user2.address);
            
            expect(user1Balance).to.equal(ethers.utils.parseEther("90"));
            expect(user2Balance).to.equal(ethers.utils.parseEther("10"));
        });

        it("应该能够每日签到", async function () {
            // 先设置用户资料
            await zeroGasDemo.connect(user1).updateProfile("Alice", 500);
            
            // 检查是否可以签到
            const canCheckIn = await zeroGasDemo.canCheckIn(user1.address);
            expect(canCheckIn).to.be.true;

            // 执行签到
            await zeroGasDemo.connect(user1).dailyCheckIn();
            
            // 检查代币余额增加
            const balance = await zeroGasDemo.balanceOf(user1.address);
            expect(balance).to.equal(ethers.utils.parseEther("30")); // level 3 * 10
        });
    });

    describe("Meta Transaction 测试", function () {
        it("应该能够验证 Meta Transaction 签名", async function () {
            const functionSignature = zeroGasDemo.interface.encodeFunctionData(
                "updateProfile", 
                ["Bob", 200]
            );

            const nonce = await zeroGasDemo.getNonce(user1.address);
            
            // 构建 Meta Transaction
            const metaTx = {
                nonce: nonce.toNumber(),
                from: user1.address,
                functionSignature: functionSignature
            };

            // 生成签名
            const domain = {
                name: 'ZeroGasManager',
                version: '1.0.0',
                chainId: (await ethers.provider.getNetwork()).chainId,
                verifyingContract: zeroGasDemo.address
            };

            const types = {
                MetaTransaction: [
                    { name: 'nonce', type: 'uint256' },
                    { name: 'from', type: 'address' },
                    { name: 'functionSignature', type: 'bytes' }
                ]
            };

            const signature = await user1._signTypedData(domain, types, metaTx);
            const { v, r, s } = ethers.utils.splitSignature(signature);

            // 执行 Meta Transaction
            await zeroGasDemo.connect(relayer).executeMetaTransaction(
                user1.address,
                functionSignature,
                r,
                s,
                v
            );

            // 验证结果
            const profile = await zeroGasDemo.getProfile(user1.address);
            expect(profile.name).to.equal("Bob");
            expect(profile.score).to.equal(200);
        });
    });

    describe("0 Gas 费功能测试", function () {
        it("应该能够检查用户免费交易资格", async function () {
            const canExecute = await zeroGasDemo.canUserExecuteFreeTransaction(user1.address);
            expect(canExecute).to.be.true;

            const remaining = await zeroGasDemo.getUserRemainingTransactions(user1.address);
            expect(remaining).to.equal(10); // 默认每日 10 次
        });

        it("应该能够执行 0 Gas 费交易", async function () {
            const functionSignature = zeroGasDemo.interface.encodeFunctionData(
                "updateProfile", 
                ["Charlie", 300]
            );

            const nonce = await zeroGasDemo.getNonce(user1.address);
            
            const metaTx = {
                nonce: nonce.toNumber(),
                from: user1.address,
                functionSignature: functionSignature
            };

            const domain = {
                name: 'ZeroGasManager',
                version: '1.0.0',
                chainId: (await ethers.provider.getNetwork()).chainId,
                verifyingContract: zeroGasDemo.address
            };

            const types = {
                MetaTransaction: [
                    { name: 'nonce', type: 'uint256' },
                    { name: 'from', type: 'address' },
                    { name: 'functionSignature', type: 'bytes' }
                ]
            };

            const signature = await user1._signTypedData(domain, types, metaTx);
            const { v, r, s } = ethers.utils.splitSignature(signature);

            // 执行 0 Gas 费交易
            await zeroGasDemo.connect(relayer).executeZeroGasTransaction(
                user1.address,
                functionSignature,
                r,
                s,
                v,
                500000 // gasLimit
            );

            // 验证结果
            const profile = await zeroGasDemo.getProfile(user1.address);
            expect(profile.name).to.equal("Charlie");
            expect(profile.score).to.equal(300);

            // 检查剩余交易次数
            const remaining = await zeroGasDemo.getUserRemainingTransactions(user1.address);
            expect(remaining).to.equal(9);
        });

        it("应该能够升级用户为 VIP", async function () {
            await zeroGasDemo.upgradeToVIP(user1.address);
            
            const remaining = await zeroGasDemo.getUserRemainingTransactions(user1.address);
            expect(remaining).to.equal(100); // VIP 每日 100 次
        });
    });

    describe("Gas 池管理测试", function () {
        it("应该能够向 Gas 池存款", async function () {
            const depositAmount = ethers.utils.parseEther("2");
            
            await zeroGasDemo.connect(user1).depositToGasPool({ value: depositAmount });
            
            const gasPoolStatus = await zeroGasDemo.getGasPoolStatus();
            expect(gasPoolStatus.totalDeposits).to.equal(ethers.utils.parseEther("7")); // 5 + 2
        });

        it("应该能够检查 Gas 池是否能覆盖成本", async function () {
            const cost = ethers.utils.parseEther("0.1");
            const canCover = await zeroGasDemo.canGasPoolCover(cost);
            expect(canCover).to.be.true;
        });

        it("应该能够获取 Gas 池状态", async function () {
            const status = await zeroGasDemo.getGasPoolStatus();
            
            expect(status.totalDeposits).to.equal(ethers.utils.parseEther("5"));
            expect(status.availableBalance).to.equal(ethers.utils.parseEther("5"));
            expect(status.dailyLimit).to.equal(ethers.utils.parseEther("10"));
        });
    });

    describe("Relayer 服务测试", function () {
        it("应该能够注册 Relayer", async function () {
            const stakeAmount = ethers.utils.parseEther("1");
            
            await relayerService.connect(user1).registerRelayer("https://api.relayer1.com", {
                value: stakeAmount
            });
            
            const relayerInfo = await relayerService.getRelayerDetails(user1.address);
            expect(relayerInfo.isActive).to.be.true;
            expect(relayerInfo.stake).to.equal(stakeAmount);
            expect(relayerInfo.endpoint).to.equal("https://api.relayer1.com");
        });

        it("应该能够获取推荐的 Relayer", async function () {
            // 注册几个 Relayer
            await relayerService.connect(user1).registerRelayer("https://api.relayer1.com", {
                value: ethers.utils.parseEther("1")
            });
            
            await relayerService.connect(user2).registerRelayer("https://api.relayer2.com", {
                value: ethers.utils.parseEther("2")
            });
            
            const recommendedRelayer = await relayerService.getRecommendedRelayer();
            expect(recommendedRelayer).to.not.equal(ethers.constants.AddressZero);
        });
    });

    describe("安全性测试", function () {
        it("应该防止重放攻击", async function () {
            const functionSignature = zeroGasDemo.interface.encodeFunctionData(
                "updateProfile", 
                ["Dave", 400]
            );

            const nonce = await zeroGasDemo.getNonce(user1.address);
            
            const metaTx = {
                nonce: nonce.toNumber(),
                from: user1.address,
                functionSignature: functionSignature
            };

            const domain = {
                name: 'ZeroGasManager',
                version: '1.0.0',
                chainId: (await ethers.provider.getNetwork()).chainId,
                verifyingContract: zeroGasDemo.address
            };

            const types = {
                MetaTransaction: [
                    { name: 'nonce', type: 'uint256' },
                    { name: 'from', type: 'address' },
                    { name: 'functionSignature', type: 'bytes' }
                ]
            };

            const signature = await user1._signTypedData(domain, types, metaTx);
            const { v, r, s } = ethers.utils.splitSignature(signature);

            // 第一次执行应该成功
            await zeroGasDemo.connect(relayer).executeMetaTransaction(
                user1.address,
                functionSignature,
                r,
                s,
                v
            );

            // 第二次执行相同的交易应该失败（nonce 已使用）
            await expect(
                zeroGasDemo.connect(relayer).executeMetaTransaction(
                    user1.address,
                    functionSignature,
                    r,
                    s,
                    v
                )
            ).to.be.revertedWith("Signer and signature do not match");
        });

        it("应该只允许授权的 Relayer 执行交易", async function () {
            const functionSignature = zeroGasDemo.interface.encodeFunctionData(
                "updateProfile", 
                ["Eve", 500]
            );

            const nonce = await zeroGasDemo.getNonce(user1.address);
            
            const metaTx = {
                nonce: nonce.toNumber(),
                from: user1.address,
                functionSignature: functionSignature
            };

            const domain = {
                name: 'ZeroGasManager',
                version: '1.0.0',
                chainId: (await ethers.provider.getNetwork()).chainId,
                verifyingContract: zeroGasDemo.address
            };

            const types = {
                MetaTransaction: [
                    { name: 'nonce', type: 'uint256' },
                    { name: 'from', type: 'address' },
                    { name: 'functionSignature', type: 'bytes' }
                ]
            };

            const signature = await user1._signTypedData(domain, types, metaTx);
            const { v, r, s } = ethers.utils.splitSignature(signature);

            // 未授权的用户尝试执行应该失败
            await expect(
                zeroGasDemo.connect(user2).executeZeroGasTransaction(
                    user1.address,
                    functionSignature,
                    r,
                    s,
                    v,
                    500000
                )
            ).to.be.revertedWith("Not authorized relayer");
        });
    });
});
