// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "@openzeppelin/contracts/utils/cryptography/EIP712.sol";

/**
 * @title MetaTransactionBase
 * @dev 基于 EIP-2771 的 Meta Transaction 基础实现
 * @notice 允许用户通过第三方 Relayer 执行无 Gas 费交易
 */
abstract contract MetaTransactionBase is EIP712 {
    using ECDSA for bytes32;

    // Meta Transaction 结构
    struct MetaTransaction {
        uint256 nonce;
        address from;
        bytes functionSignature;
    }

    // 用户 nonce 映射
    mapping(address => uint256) public nonces;
    
    // 可信的 Forwarder 地址
    mapping(address => bool) public trustedForwarders;
    
    // 事件
    event MetaTransactionExecuted(
        address indexed userAddress,
        address indexed relayerAddress,
        bytes functionSignature
    );

    // EIP-712 类型哈希
    bytes32 private constant META_TRANSACTION_TYPEHASH = 
        keccak256("MetaTransaction(uint256 nonce,address from,bytes functionSignature)");

    constructor(string memory name, string memory version) 
        EIP712(name, version) 
    {
        // 初始化可信 Forwarder（可以是 Biconomy 等服务）
        trustedForwarders[msg.sender] = true;
    }

    /**
     * @dev 修饰符：检查是否为可信 Forwarder
     */
    modifier onlyTrustedForwarder() {
        require(trustedForwarders[msg.sender], "Not a trusted forwarder");
        _;
    }

    /**
     * @dev 添加可信 Forwarder
     */
    function addTrustedForwarder(address forwarder) external {
        require(msg.sender == address(this), "Only self");
        trustedForwarders[forwarder] = true;
    }

    /**
     * @dev 移除可信 Forwarder
     */
    function removeTrustedForwarder(address forwarder) external {
        require(msg.sender == address(this), "Only self");
        trustedForwarders[forwarder] = false;
    }

    /**
     * @dev 执行 Meta Transaction
     * @param userAddress 原始用户地址
     * @param functionSignature 要执行的函数签名
     * @param sigR 签名的 r 部分
     * @param sigS 签名的 s 部分
     * @param sigV 签名的 v 部分
     */
    function executeMetaTransaction(
        address userAddress,
        bytes memory functionSignature,
        bytes32 sigR,
        bytes32 sigS,
        uint8 sigV
    ) external payable onlyTrustedForwarder returns (bytes memory) {
        MetaTransaction memory metaTx = MetaTransaction({
            nonce: nonces[userAddress],
            from: userAddress,
            functionSignature: functionSignature
        });

        // 验证签名
        require(
            verify(userAddress, metaTx, sigV, sigR, sigS),
            "Signer and signature do not match"
        );

        // 增加 nonce
        nonces[userAddress]++;

        // 执行函数调用
        (bool success, bytes memory returnData) = address(this).call(
            abi.encodePacked(functionSignature, userAddress)
        );

        require(success, "Function call not successful");

        emit MetaTransactionExecuted(userAddress, msg.sender, functionSignature);
        
        return returnData;
    }

    /**
     * @dev 验证 Meta Transaction 签名
     */
    function verify(
        address signer,
        MetaTransaction memory metaTx,
        uint8 sigV,
        bytes32 sigR,
        bytes32 sigS
    ) internal view returns (bool) {
        bytes32 digest = _hashTypedDataV4(
            keccak256(
                abi.encode(
                    META_TRANSACTION_TYPEHASH,
                    metaTx.nonce,
                    metaTx.from,
                    keccak256(metaTx.functionSignature)
                )
            )
        );
        
        address recoveredSigner = digest.recover(sigV, sigR, sigS);
        return recoveredSigner == signer;
    }

    /**
     * @dev 获取当前发送者地址（支持 Meta Transaction）
     */
    function _msgSender() internal view virtual override returns (address) {
        if (trustedForwarders[msg.sender]) {
            // 从 calldata 末尾提取原始发送者地址
            return abi.decode(msg.data[msg.data.length - 20:], (address));
        }
        return msg.sender;
    }

    /**
     * @dev 获取 Meta Transaction 的链上哈希（用于前端签名）
     */
    function getMetaTransactionHash(
        address user,
        bytes memory functionSignature
    ) external view returns (bytes32) {
        MetaTransaction memory metaTx = MetaTransaction({
            nonce: nonces[user],
            from: user,
            functionSignature: functionSignature
        });

        return _hashTypedDataV4(
            keccak256(
                abi.encode(
                    META_TRANSACTION_TYPEHASH,
                    metaTx.nonce,
                    metaTx.from,
                    keccak256(metaTx.functionSignature)
                )
            )
        );
    }

    /**
     * @dev 获取用户当前 nonce
     */
    function getNonce(address user) external view returns (uint256) {
        return nonces[user];
    }
}
