// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./MetaTransactionBase.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title ZeroGasManager
 * @dev 0 Gas 费交易管理合约
 * @notice 结合 Meta Transaction 和 Gas 费补贴机制实现真正的 0 Gas 费体验
 */
contract ZeroGasManager is MetaTransactionBase, ReentrancyGuard, Ownable, Pausable {
    
    // Gas 费补贴池
    struct GasPool {
        uint256 totalDeposits;      // 总存款
        uint256 totalSpent;         // 总支出
        uint256 dailyLimit;         // 每日限额
        uint256 lastResetTime;      // 上次重置时间
        uint256 todaySpent;         // 今日已支出
        bool active;                // 是否激活
    }

    // 用户 Gas 费补贴信息
    struct UserGasInfo {
        uint256 freeTransactions;   // 免费交易次数
        uint256 usedTransactions;   // 已使用次数
        uint256 lastResetTime;      // 上次重置时间
        bool isVIP;                 // 是否 VIP 用户
    }

    // 状态变量
    GasPool public gasPool;
    mapping(address => UserGasInfo) public userGasInfo;
    mapping(address => bool) public authorizedRelayers;
    
    // 配置参数
    uint256 public constant FREE_TX_PER_DAY = 10;        // 每日免费交易次数
    uint256 public constant VIP_TX_PER_DAY = 100;        // VIP 每日免费交易次数
    uint256 public constant MAX_GAS_PER_TX = 500000;     // 单笔交易最大 Gas
    uint256 public constant DAILY_RESET_INTERVAL = 1 days;

    // 事件
    event GasPoolDeposit(address indexed depositor, uint256 amount);
    event GasSubsidyUsed(address indexed user, uint256 gasUsed, uint256 gasCost);
    event RelayerAuthorized(address indexed relayer, bool authorized);
    event UserUpgradedToVIP(address indexed user);

    constructor() 
        MetaTransactionBase("ZeroGasManager", "1.0.0") 
    {
        // 初始化 Gas 池
        gasPool = GasPool({
            totalDeposits: 0,
            totalSpent: 0,
            dailyLimit: 10 ether,
            lastResetTime: block.timestamp,
            todaySpent: 0,
            active: true
        });
    }

    /**
     * @dev 修饰符：检查是否为授权的 Relayer
     */
    modifier onlyAuthorizedRelayer() {
        require(authorizedRelayers[msg.sender], "Not authorized relayer");
        _;
    }

    /**
     * @dev 向 Gas 池存款
     */
    function depositToGasPool() external payable {
        require(msg.value > 0, "Deposit must be greater than 0");
        
        gasPool.totalDeposits += msg.value;
        
        emit GasPoolDeposit(msg.sender, msg.value);
    }

    /**
     * @dev 授权 Relayer
     */
    function authorizeRelayer(address relayer, bool authorized) external onlyOwner {
        authorizedRelayers[relayer] = authorized;
        emit RelayerAuthorized(relayer, authorized);
    }

    /**
     * @dev 升级用户为 VIP
     */
    function upgradeToVIP(address user) external onlyOwner {
        userGasInfo[user].isVIP = true;
        emit UserUpgradedToVIP(user);
    }

    /**
     * @dev 执行 0 Gas 费交易
     */
    function executeZeroGasTransaction(
        address userAddress,
        bytes memory functionSignature,
        bytes32 sigR,
        bytes32 sigS,
        uint8 sigV,
        uint256 gasLimit
    ) external onlyAuthorizedRelayer whenNotPaused nonReentrant returns (bytes memory) {
        
        // 检查 Gas 限制
        require(gasLimit <= MAX_GAS_PER_TX, "Gas limit too high");
        
        // 检查用户是否有免费交易额度
        require(canUserExecuteFreeTransaction(userAddress), "No free transactions left");
        
        // 检查 Gas 池是否有足够资金
        uint256 estimatedCost = gasLimit * tx.gasprice;
        require(canGasPoolCover(estimatedCost), "Gas pool insufficient");
        
        // 记录 Gas 使用前的状态
        uint256 gasStart = gasleft();
        
        // 执行 Meta Transaction
        bytes memory result = executeMetaTransaction(
            userAddress,
            functionSignature,
            sigR,
            sigS,
            sigV
        );
        
        // 计算实际 Gas 消耗
        uint256 gasUsed = gasStart - gasleft() + 21000; // 加上基础交易成本
        uint256 actualCost = gasUsed * tx.gasprice;
        
        // 更新用户使用记录
        _updateUserGasUsage(userAddress);
        
        // 更新 Gas 池状态
        _updateGasPoolUsage(actualCost);
        
        // 补偿 Relayer
        payable(msg.sender).transfer(actualCost);
        
        emit GasSubsidyUsed(userAddress, gasUsed, actualCost);
        
        return result;
    }

    /**
     * @dev 检查用户是否可以执行免费交易
     */
    function canUserExecuteFreeTransaction(address user) public view returns (bool) {
        UserGasInfo memory info = userGasInfo[user];
        
        // 检查是否需要重置每日计数
        if (block.timestamp >= info.lastResetTime + DAILY_RESET_INTERVAL) {
            return true; // 新的一天，可以执行
        }
        
        // 检查剩余免费交易次数
        uint256 dailyLimit = info.isVIP ? VIP_TX_PER_DAY : FREE_TX_PER_DAY;
        return info.usedTransactions < dailyLimit;
    }

    /**
     * @dev 检查 Gas 池是否能覆盖成本
     */
    function canGasPoolCover(uint256 cost) public view returns (bool) {
        if (!gasPool.active) return false;
        
        // 检查总余额
        uint256 availableBalance = gasPool.totalDeposits - gasPool.totalSpent;
        if (availableBalance < cost) return false;
        
        // 检查每日限额
        uint256 todaySpent = gasPool.todaySpent;
        if (block.timestamp >= gasPool.lastResetTime + DAILY_RESET_INTERVAL) {
            todaySpent = 0; // 新的一天
        }
        
        return (todaySpent + cost) <= gasPool.dailyLimit;
    }

    /**
     * @dev 更新用户 Gas 使用记录
     */
    function _updateUserGasUsage(address user) internal {
        UserGasInfo storage info = userGasInfo[user];
        
        // 检查是否需要重置每日计数
        if (block.timestamp >= info.lastResetTime + DAILY_RESET_INTERVAL) {
            info.usedTransactions = 1;
            info.lastResetTime = block.timestamp;
        } else {
            info.usedTransactions++;
        }
    }

    /**
     * @dev 更新 Gas 池使用记录
     */
    function _updateGasPoolUsage(uint256 cost) internal {
        // 检查是否需要重置每日计数
        if (block.timestamp >= gasPool.lastResetTime + DAILY_RESET_INTERVAL) {
            gasPool.todaySpent = cost;
            gasPool.lastResetTime = block.timestamp;
        } else {
            gasPool.todaySpent += cost;
        }
        
        gasPool.totalSpent += cost;
    }

    /**
     * @dev 获取用户剩余免费交易次数
     */
    function getUserRemainingTransactions(address user) external view returns (uint256) {
        UserGasInfo memory info = userGasInfo[user];
        
        // 如果是新的一天，返回完整额度
        if (block.timestamp >= info.lastResetTime + DAILY_RESET_INTERVAL) {
            return info.isVIP ? VIP_TX_PER_DAY : FREE_TX_PER_DAY;
        }
        
        uint256 dailyLimit = info.isVIP ? VIP_TX_PER_DAY : FREE_TX_PER_DAY;
        return dailyLimit > info.usedTransactions ? 
               dailyLimit - info.usedTransactions : 0;
    }

    /**
     * @dev 获取 Gas 池状态
     */
    function getGasPoolStatus() external view returns (
        uint256 totalDeposits,
        uint256 totalSpent,
        uint256 availableBalance,
        uint256 dailyLimit,
        uint256 todaySpent,
        uint256 todayRemaining
    ) {
        totalDeposits = gasPool.totalDeposits;
        totalSpent = gasPool.totalSpent;
        availableBalance = totalDeposits - totalSpent;
        dailyLimit = gasPool.dailyLimit;
        
        if (block.timestamp >= gasPool.lastResetTime + DAILY_RESET_INTERVAL) {
            todaySpent = 0;
        } else {
            todaySpent = gasPool.todaySpent;
        }
        
        todayRemaining = dailyLimit > todaySpent ? dailyLimit - todaySpent : 0;
    }

    /**
     * @dev 紧急暂停功能
     */
    function emergencyPause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复功能
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 提取 Gas 池资金（仅限紧急情况）
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(amount <= address(this).balance, "Insufficient balance");
        payable(owner()).transfer(amount);
    }
}
