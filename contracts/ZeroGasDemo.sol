// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./ZeroGasManager.sol";

/**
 * @title ZeroGasDemo
 * @dev 0 Gas 费功能演示合约
 * @notice 展示如何在实际应用中使用 0 Gas 费交易
 */
contract ZeroGasDemo is ZeroGasManager {
    
    // 演示数据结构
    struct UserProfile {
        string name;
        uint256 score;
        uint256 level;
        uint256 lastUpdateTime;
    }
    
    // 简单的代币余额
    mapping(address => uint256) public tokenBalances;
    mapping(address => UserProfile) public userProfiles;
    
    // 代币总供应量
    uint256 public totalSupply = 1000000 * 10**18;
    string public constant TOKEN_NAME = "ZeroGas Token";
    string public constant TOKEN_SYMBOL = "ZGT";
    uint8 public constant DECIMALS = 18;
    
    // 事件
    event ProfileUpdated(address indexed user, string name, uint256 score);
    event TokensTransferred(address indexed from, address indexed to, uint256 amount);
    event TokensMinted(address indexed to, uint256 amount);
    
    constructor() {
        // 给合约部署者一些初始代币
        tokenBalances[msg.sender] = totalSupply;
    }
    
    /**
     * @dev 更新用户资料（0 Gas 费功能）
     * @notice 用户可以通过 Meta Transaction 免费更新资料
     */
    function updateProfile(string memory name, uint256 score) external {
        address user = _msgSender(); // 支持 Meta Transaction
        
        userProfiles[user] = UserProfile({
            name: name,
            score: score,
            level: _calculateLevel(score),
            lastUpdateTime: block.timestamp
        });
        
        emit ProfileUpdated(user, name, score);
    }
    
    /**
     * @dev 转账代币（0 Gas 费功能）
     */
    function transfer(address to, uint256 amount) external returns (bool) {
        address from = _msgSender(); // 支持 Meta Transaction
        
        require(tokenBalances[from] >= amount, "Insufficient balance");
        require(to != address(0), "Transfer to zero address");
        
        tokenBalances[from] -= amount;
        tokenBalances[to] += amount;
        
        emit TokensTransferred(from, to, amount);
        return true;
    }
    
    /**
     * @dev 铸造代币给用户（仅限管理员）
     */
    function mintTokens(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Mint to zero address");
        
        tokenBalances[to] += amount;
        totalSupply += amount;
        
        emit TokensMinted(to, amount);
    }
    
    /**
     * @dev 每日签到获得代币（0 Gas 费功能）
     */
    function dailyCheckIn() external {
        address user = _msgSender(); // 支持 Meta Transaction
        UserProfile storage profile = userProfiles[user];
        
        // 检查是否已经签到过
        require(
            block.timestamp >= profile.lastUpdateTime + 1 days,
            "Already checked in today"
        );
        
        // 更新签到时间
        profile.lastUpdateTime = block.timestamp;
        
        // 根据等级给予不同奖励
        uint256 reward = _calculateDailyReward(profile.level);
        tokenBalances[user] += reward;
        
        emit TokensMinted(user, reward);
    }
    
    /**
     * @dev 批量操作（0 Gas 费功能）
     * @notice 演示如何在一个交易中执行多个操作
     */
    function batchOperations(
        string memory name,
        uint256 score,
        address[] memory recipients,
        uint256[] memory amounts
    ) external {
        address user = _msgSender(); // 支持 Meta Transaction
        
        require(recipients.length == amounts.length, "Arrays length mismatch");
        
        // 更新资料
        userProfiles[user] = UserProfile({
            name: name,
            score: score,
            level: _calculateLevel(score),
            lastUpdateTime: block.timestamp
        });
        
        // 批量转账
        for (uint256 i = 0; i < recipients.length; i++) {
            require(tokenBalances[user] >= amounts[i], "Insufficient balance");
            require(recipients[i] != address(0), "Transfer to zero address");
            
            tokenBalances[user] -= amounts[i];
            tokenBalances[recipients[i]] += amounts[i];
            
            emit TokensTransferred(user, recipients[i], amounts[i]);
        }
        
        emit ProfileUpdated(user, name, score);
    }
    
    /**
     * @dev 计算用户等级
     */
    function _calculateLevel(uint256 score) internal pure returns (uint256) {
        if (score < 100) return 1;
        if (score < 500) return 2;
        if (score < 1000) return 3;
        if (score < 5000) return 4;
        return 5;
    }
    
    /**
     * @dev 计算每日签到奖励
     */
    function _calculateDailyReward(uint256 level) internal pure returns (uint256) {
        return level * 10 * 10**18; // 等级 * 10 个代币
    }
    
    /**
     * @dev 获取用户代币余额
     */
    function balanceOf(address user) external view returns (uint256) {
        return tokenBalances[user];
    }
    
    /**
     * @dev 获取用户资料
     */
    function getProfile(address user) external view returns (
        string memory name,
        uint256 score,
        uint256 level,
        uint256 lastUpdateTime
    ) {
        UserProfile memory profile = userProfiles[user];
        return (profile.name, profile.score, profile.level, profile.lastUpdateTime);
    }
    
    /**
     * @dev 检查用户是否可以签到
     */
    function canCheckIn(address user) external view returns (bool) {
        UserProfile memory profile = userProfiles[user];
        return block.timestamp >= profile.lastUpdateTime + 1 days;
    }
    
    /**
     * @dev 获取下次签到奖励
     */
    function getNextCheckInReward(address user) external view returns (uint256) {
        UserProfile memory profile = userProfiles[user];
        return _calculateDailyReward(profile.level);
    }
    
    /**
     * @dev 模拟复杂计算（测试 Gas 消耗）
     */
    function complexCalculation(uint256 iterations) external view returns (uint256) {
        uint256 result = 0;
        for (uint256 i = 0; i < iterations; i++) {
            result += i * i + i;
        }
        return result;
    }
    
    /**
     * @dev 获取合约统计信息
     */
    function getContractStats() external view returns (
        uint256 _totalSupply,
        uint256 contractBalance,
        uint256 totalUsers
    ) {
        _totalSupply = totalSupply;
        contractBalance = address(this).balance;
        
        // 这里简化处理，实际应用中可能需要维护用户计数
        totalUsers = 0;
    }
}
