// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title RelayerService
 * @dev Relayer 服务合约，负责管理和执行 Meta Transaction
 * @notice 提供去中心化的 Relayer 网络服务
 */
contract RelayerService is Ownable, ReentrancyGuard, Pausable {
    
    // Relayer 信息结构
    struct RelayerInfo {
        bool isActive;              // 是否激活
        uint256 stake;              // 质押金额
        uint256 reputation;         // 信誉分数
        uint256 totalTransactions;  // 总交易数
        uint256 successfulTx;       // 成功交易数
        uint256 lastActiveTime;     // 最后活跃时间
        string endpoint;            // API 端点
    }
    
    // 交易请求结构
    struct TransactionRequest {
        address target;             // 目标合约
        bytes data;                 // 交易数据
        uint256 gasLimit;           // Gas 限制
        uint256 maxFeePerGas;       // 最大 Gas 费
        uint256 deadline;           // 截止时间
        address requester;          // 请求者
        bool executed;              // 是否已执行
    }
    
    // 状态变量
    mapping(address => RelayerInfo) public relayers;
    mapping(bytes32 => TransactionRequest) public transactionRequests;
    mapping(address => uint256) public relayerEarnings;
    
    address[] public activeRelayers;
    uint256 public minimumStake = 1 ether;
    uint256 public minimumReputation = 100;
    uint256 public maxGasLimit = 1000000;
    uint256 public serviceFeeRate = 100; // 1% (basis points)
    
    // 事件
    event RelayerRegistered(address indexed relayer, uint256 stake, string endpoint);
    event RelayerDeactivated(address indexed relayer);
    event TransactionRequested(bytes32 indexed requestId, address indexed requester);
    event TransactionExecuted(bytes32 indexed requestId, address indexed relayer, bool success);
    event RelayerSlashed(address indexed relayer, uint256 amount, string reason);
    
    constructor() {}
    
    /**
     * @dev 注册成为 Relayer
     */
    function registerRelayer(string memory endpoint) external payable {
        require(msg.value >= minimumStake, "Insufficient stake");
        require(!relayers[msg.sender].isActive, "Already registered");
        require(bytes(endpoint).length > 0, "Invalid endpoint");
        
        relayers[msg.sender] = RelayerInfo({
            isActive: true,
            stake: msg.value,
            reputation: 100, // 初始信誉分数
            totalTransactions: 0,
            successfulTx: 0,
            lastActiveTime: block.timestamp,
            endpoint: endpoint
        });
        
        activeRelayers.push(msg.sender);
        
        emit RelayerRegistered(msg.sender, msg.value, endpoint);
    }
    
    /**
     * @dev 增加质押
     */
    function addStake() external payable {
        require(relayers[msg.sender].isActive, "Not an active relayer");
        relayers[msg.sender].stake += msg.value;
    }
    
    /**
     * @dev 退出 Relayer 服务
     */
    function deactivateRelayer() external {
        require(relayers[msg.sender].isActive, "Not an active relayer");
        
        RelayerInfo storage relayer = relayers[msg.sender];
        relayer.isActive = false;
        
        // 移除活跃列表
        _removeFromActiveList(msg.sender);
        
        // 退还质押（可能需要等待期）
        payable(msg.sender).transfer(relayer.stake);
        relayer.stake = 0;
        
        emit RelayerDeactivated(msg.sender);
    }
    
    /**
     * @dev 提交交易请求
     */
    function submitTransactionRequest(
        address target,
        bytes memory data,
        uint256 gasLimit,
        uint256 maxFeePerGas,
        uint256 deadline
    ) external returns (bytes32 requestId) {
        require(gasLimit <= maxGasLimit, "Gas limit too high");
        require(deadline > block.timestamp, "Invalid deadline");
        
        requestId = keccak256(abi.encodePacked(
            msg.sender,
            target,
            data,
            gasLimit,
            block.timestamp,
            block.number
        ));
        
        transactionRequests[requestId] = TransactionRequest({
            target: target,
            data: data,
            gasLimit: gasLimit,
            maxFeePerGas: maxFeePerGas,
            deadline: deadline,
            requester: msg.sender,
            executed: false
        });
        
        emit TransactionRequested(requestId, msg.sender);
        return requestId;
    }
    
    /**
     * @dev Relayer 执行交易请求
     */
    function executeTransactionRequest(bytes32 requestId) 
        external 
        nonReentrant 
        whenNotPaused 
    {
        require(relayers[msg.sender].isActive, "Not an active relayer");
        
        TransactionRequest storage request = transactionRequests[requestId];
        require(!request.executed, "Already executed");
        require(block.timestamp <= request.deadline, "Request expired");
        
        // 标记为已执行
        request.executed = true;
        
        // 记录 Gas 使用前的状态
        uint256 gasStart = gasleft();
        
        // 执行交易
        (bool success, ) = request.target.call{gas: request.gasLimit}(request.data);
        
        // 计算实际 Gas 消耗
        uint256 gasUsed = gasStart - gasleft() + 21000;
        uint256 gasCost = gasUsed * tx.gasprice;
        
        // 更新 Relayer 统计
        RelayerInfo storage relayer = relayers[msg.sender];
        relayer.totalTransactions++;
        relayer.lastActiveTime = block.timestamp;
        
        if (success) {
            relayer.successfulTx++;
            relayer.reputation += 1;
            
            // 计算服务费
            uint256 serviceFee = gasCost * serviceFeeRate / 10000;
            relayerEarnings[msg.sender] += gasCost + serviceFee;
        } else {
            // 失败时降低信誉
            if (relayer.reputation > 0) {
                relayer.reputation -= 1;
            }
        }
        
        emit TransactionExecuted(requestId, msg.sender, success);
    }
    
    /**
     * @dev 获取推荐的 Relayer
     */
    function getRecommendedRelayer() external view returns (address) {
        require(activeRelayers.length > 0, "No active relayers");
        
        address bestRelayer = address(0);
        uint256 bestScore = 0;
        
        for (uint256 i = 0; i < activeRelayers.length; i++) {
            address relayerAddr = activeRelayers[i];
            RelayerInfo memory relayer = relayers[relayerAddr];
            
            if (!relayer.isActive || relayer.reputation < minimumReputation) {
                continue;
            }
            
            // 计算综合评分：信誉 + 成功率 + 活跃度
            uint256 successRate = relayer.totalTransactions > 0 ? 
                (relayer.successfulTx * 100) / relayer.totalTransactions : 0;
            
            uint256 activityScore = block.timestamp - relayer.lastActiveTime < 1 hours ? 100 : 0;
            
            uint256 score = relayer.reputation + successRate + activityScore;
            
            if (score > bestScore) {
                bestScore = score;
                bestRelayer = relayerAddr;
            }
        }
        
        return bestRelayer;
    }
    
    /**
     * @dev 获取多个推荐 Relayer
     */
    function getMultipleRelayers(uint256 count) external view returns (address[] memory) {
        require(count > 0 && count <= activeRelayers.length, "Invalid count");
        
        address[] memory recommended = new address[](count);
        bool[] memory used = new bool[](activeRelayers.length);
        
        for (uint256 i = 0; i < count; i++) {
            uint256 bestIndex = 0;
            uint256 bestScore = 0;
            
            for (uint256 j = 0; j < activeRelayers.length; j++) {
                if (used[j]) continue;
                
                address relayerAddr = activeRelayers[j];
                RelayerInfo memory relayer = relayers[relayerAddr];
                
                if (!relayer.isActive || relayer.reputation < minimumReputation) {
                    continue;
                }
                
                uint256 successRate = relayer.totalTransactions > 0 ? 
                    (relayer.successfulTx * 100) / relayer.totalTransactions : 0;
                
                uint256 score = relayer.reputation + successRate;
                
                if (score > bestScore) {
                    bestScore = score;
                    bestIndex = j;
                }
            }
            
            if (bestScore > 0) {
                recommended[i] = activeRelayers[bestIndex];
                used[bestIndex] = true;
            }
        }
        
        return recommended;
    }
    
    /**
     * @dev Relayer 提取收益
     */
    function withdrawEarnings() external {
        uint256 earnings = relayerEarnings[msg.sender];
        require(earnings > 0, "No earnings to withdraw");
        
        relayerEarnings[msg.sender] = 0;
        payable(msg.sender).transfer(earnings);
    }
    
    /**
     * @dev 惩罚恶意 Relayer（仅限管理员）
     */
    function slashRelayer(address relayer, uint256 amount, string memory reason) 
        external 
        onlyOwner 
    {
        require(relayers[relayer].isActive, "Relayer not active");
        require(amount <= relayers[relayer].stake, "Slash amount too high");
        
        relayers[relayer].stake -= amount;
        relayers[relayer].reputation = relayers[relayer].reputation > 10 ? 
            relayers[relayer].reputation - 10 : 0;
        
        emit RelayerSlashed(relayer, amount, reason);
    }
    
    /**
     * @dev 更新配置参数
     */
    function updateConfig(
        uint256 _minimumStake,
        uint256 _minimumReputation,
        uint256 _maxGasLimit,
        uint256 _serviceFeeRate
    ) external onlyOwner {
        minimumStake = _minimumStake;
        minimumReputation = _minimumReputation;
        maxGasLimit = _maxGasLimit;
        serviceFeeRate = _serviceFeeRate;
    }
    
    /**
     * @dev 从活跃列表中移除 Relayer
     */
    function _removeFromActiveList(address relayer) internal {
        for (uint256 i = 0; i < activeRelayers.length; i++) {
            if (activeRelayers[i] == relayer) {
                activeRelayers[i] = activeRelayers[activeRelayers.length - 1];
                activeRelayers.pop();
                break;
            }
        }
    }
    
    /**
     * @dev 获取活跃 Relayer 数量
     */
    function getActiveRelayerCount() external view returns (uint256) {
        return activeRelayers.length;
    }
    
    /**
     * @dev 获取 Relayer 详细信息
     */
    function getRelayerDetails(address relayer) external view returns (
        bool isActive,
        uint256 stake,
        uint256 reputation,
        uint256 totalTransactions,
        uint256 successfulTx,
        uint256 successRate,
        string memory endpoint
    ) {
        RelayerInfo memory info = relayers[relayer];
        successRate = info.totalTransactions > 0 ? 
            (info.successfulTx * 100) / info.totalTransactions : 0;
        
        return (
            info.isActive,
            info.stake,
            info.reputation,
            info.totalTransactions,
            info.successfulTx,
            successRate,
            info.endpoint
        );
    }
}
