/**
 * ZeroGas SDK - 0 Gas 费交易 JavaScript SDK
 * 提供简单易用的 API 来执行无 Gas 费交易
 */

const ethers = require('ethers');

class ZeroGasSDK {
    constructor(config) {
        this.config = {
            rpcUrl: config.rpcUrl,
            relayerUrl: config.relayerUrl || 'https://api.zerogas.io',
            contractAddress: config.contractAddress,
            chainId: config.chainId || 1,
            ...config
        };
        
        this.provider = new ethers.providers.JsonRpcProvider(this.config.rpcUrl);
        this.contract = null;
        this.signer = null;
    }

    /**
     * 初始化 SDK
     */
    async initialize(contractABI) {
        this.contract = new ethers.Contract(
            this.config.contractAddress,
            contractABI,
            this.provider
        );
    }

    /**
     * 设置用户钱包
     */
    setSigner(signer) {
        this.signer = signer;
        this.contract = this.contract.connect(signer);
    }

    /**
     * 执行 0 Gas 费交易
     */
    async executeZeroGasTransaction(functionName, params = [], options = {}) {
        try {
            if (!this.signer) {
                throw new Error('Signer not set. Call setSigner() first.');
            }

            const userAddress = await this.signer.getAddress();
            
            // 1. 构建函数调用数据
            const functionSignature = this.contract.interface.encodeFunctionData(
                functionName, 
                params
            );

            // 2. 获取用户 nonce
            const nonce = await this.contract.getNonce(userAddress);

            // 3. 构建 Meta Transaction
            const metaTx = {
                nonce: nonce.toNumber(),
                from: userAddress,
                functionSignature: functionSignature
            };

            // 4. 生成签名
            const signature = await this._signMetaTransaction(metaTx);

            // 5. 提交到 Relayer
            const result = await this._submitToRelayer({
                userAddress,
                functionSignature,
                signature,
                gasLimit: options.gasLimit || 500000,
                ...options
            });

            return result;

        } catch (error) {
            console.error('ZeroGas transaction failed:', error);
            throw error;
        }
    }

    /**
     * 批量执行多个 0 Gas 费交易
     */
    async executeBatchZeroGasTransactions(transactions, options = {}) {
        try {
            const results = [];
            
            for (const tx of transactions) {
                const result = await this.executeZeroGasTransaction(
                    tx.functionName,
                    tx.params,
                    { ...options, ...tx.options }
                );
                results.push(result);
                
                // 添加延迟避免 nonce 冲突
                if (options.delay) {
                    await this._delay(options.delay);
                }
            }
            
            return results;
        } catch (error) {
            console.error('Batch ZeroGas transactions failed:', error);
            throw error;
        }
    }

    /**
     * 检查用户是否可以执行免费交易
     */
    async canExecuteFreeTransaction(userAddress) {
        try {
            return await this.contract.canUserExecuteFreeTransaction(userAddress);
        } catch (error) {
            console.error('Failed to check free transaction eligibility:', error);
            return false;
        }
    }

    /**
     * 获取用户剩余免费交易次数
     */
    async getRemainingTransactions(userAddress) {
        try {
            const remaining = await this.contract.getUserRemainingTransactions(userAddress);
            return remaining.toNumber();
        } catch (error) {
            console.error('Failed to get remaining transactions:', error);
            return 0;
        }
    }

    /**
     * 获取 Gas 池状态
     */
    async getGasPoolStatus() {
        try {
            const status = await this.contract.getGasPoolStatus();
            return {
                totalDeposits: ethers.utils.formatEther(status.totalDeposits),
                totalSpent: ethers.utils.formatEther(status.totalSpent),
                availableBalance: ethers.utils.formatEther(status.availableBalance),
                dailyLimit: ethers.utils.formatEther(status.dailyLimit),
                todaySpent: ethers.utils.formatEther(status.todaySpent),
                todayRemaining: ethers.utils.formatEther(status.todayRemaining)
            };
        } catch (error) {
            console.error('Failed to get gas pool status:', error);
            return null;
        }
    }

    /**
     * 向 Gas 池存款
     */
    async depositToGasPool(amount) {
        try {
            if (!this.signer) {
                throw new Error('Signer not set');
            }

            const tx = await this.contract.depositToGasPool({
                value: ethers.utils.parseEther(amount.toString())
            });

            return await tx.wait();
        } catch (error) {
            console.error('Failed to deposit to gas pool:', error);
            throw error;
        }
    }

    /**
     * 获取推荐的 Relayer
     */
    async getRecommendedRelayer() {
        try {
            const response = await fetch(`${this.config.relayerUrl}/api/relayers/recommended`);
            const data = await response.json();
            return data.relayer;
        } catch (error) {
            console.error('Failed to get recommended relayer:', error);
            return null;
        }
    }

    /**
     * 估算交易 Gas 费用
     */
    async estimateGasCost(functionName, params = []) {
        try {
            const gasEstimate = await this.contract.estimateGas[functionName](...params);
            const gasPrice = await this.provider.getGasPrice();
            const gasCost = gasEstimate.mul(gasPrice);
            
            return {
                gasLimit: gasEstimate.toNumber(),
                gasPrice: ethers.utils.formatUnits(gasPrice, 'gwei'),
                gasCost: ethers.utils.formatEther(gasCost)
            };
        } catch (error) {
            console.error('Failed to estimate gas cost:', error);
            return null;
        }
    }

    /**
     * 监听交易状态
     */
    async waitForTransaction(txHash, timeout = 60000) {
        try {
            const receipt = await this.provider.waitForTransaction(txHash, 1, timeout);
            return receipt;
        } catch (error) {
            console.error('Transaction timeout or failed:', error);
            throw error;
        }
    }

    /**
     * 生成 Meta Transaction 签名
     */
    async _signMetaTransaction(metaTx) {
        const domain = {
            name: 'ZeroGasManager',
            version: '1.0.0',
            chainId: this.config.chainId,
            verifyingContract: this.config.contractAddress
        };

        const types = {
            MetaTransaction: [
                { name: 'nonce', type: 'uint256' },
                { name: 'from', type: 'address' },
                { name: 'functionSignature', type: 'bytes' }
            ]
        };

        const signature = await this.signer._signTypedData(domain, types, metaTx);
        return ethers.utils.splitSignature(signature);
    }

    /**
     * 提交交易到 Relayer
     */
    async _submitToRelayer(txData) {
        try {
            const response = await fetch(`${this.config.relayerUrl}/api/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chainId: this.config.chainId,
                    contractAddress: this.config.contractAddress,
                    ...txData
                })
            });

            if (!response.ok) {
                throw new Error(`Relayer error: ${response.statusText}`);
            }

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Failed to submit to relayer:', error);
            throw error;
        }
    }

    /**
     * 延迟函数
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取交易历史
     */
    async getTransactionHistory(userAddress, limit = 10) {
        try {
            const response = await fetch(
                `${this.config.relayerUrl}/api/transactions/${userAddress}?limit=${limit}`
            );
            const data = await response.json();
            return data.transactions;
        } catch (error) {
            console.error('Failed to get transaction history:', error);
            return [];
        }
    }

    /**
     * 获取用户统计信息
     */
    async getUserStats(userAddress) {
        try {
            const [remaining, canExecute, gasPoolStatus] = await Promise.all([
                this.getRemainingTransactions(userAddress),
                this.canExecuteFreeTransaction(userAddress),
                this.getGasPoolStatus()
            ]);

            return {
                remainingTransactions: remaining,
                canExecuteFreeTransaction: canExecute,
                gasPoolStatus: gasPoolStatus
            };
        } catch (error) {
            console.error('Failed to get user stats:', error);
            return null;
        }
    }
}

// 导出 SDK
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ZeroGasSDK;
} else if (typeof window !== 'undefined') {
    window.ZeroGasSDK = ZeroGasSDK;
}

// 使用示例
/*
const sdk = new ZeroGasSDK({
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
    relayerUrl: 'https://api.zerogas.io',
    contractAddress: '0x...',
    chainId: 1
});

// 初始化
await sdk.initialize(contractABI);
sdk.setSigner(signer);

// 执行 0 Gas 费交易
const result = await sdk.executeZeroGasTransaction('updateProfile', ['Alice', 100]);

// 批量执行
const batchResult = await sdk.executeBatchZeroGasTransactions([
    { functionName: 'updateProfile', params: ['Alice', 100] },
    { functionName: 'transfer', params: ['0x...', ethers.utils.parseEther('10')] }
]);
*/
