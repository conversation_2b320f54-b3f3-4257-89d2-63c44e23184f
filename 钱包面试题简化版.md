# 区块链钱包面试题目

## 1.描述一下交易钱包的充值和提现流程

### 充值

- 交易所给用户提供地址，用户把钱转进来
- 扫链：获取最新块高，从上一次解析的块开始到最新块高进行交易解析
- 交易解析完之后，如果交易里面 to 是系统用户，则是充值，解析完成之后上账，并通知业务层

### 提现

- 获取需要的签名参数
- 交易离线签名：组织交易，生成待签名消息摘要，将待签名的消息摘要递给签名机进行签名，签名机签名完成之后返回签名串
- 构建完整的交易并发送区块链网络，将签名时计算出来的交易 Hash 或者发送交易时返回交易 Hash 更新到数据库
- 扫链解析到这笔交易，说明提现成功。

## 2.HD 钱包助记词生成的流程

- 第一步：随机熵生成
- 第二步：计算校验和
- 第三步：组合熵和校验和
- 第四步：分割助记词索引
- 第五步：映射为助记词

## 3.助记词的验证过程

- 第一步：检查助记词数量
- 第二步：检查助记词是否在词汇表
- 第三步：将助记词转换成索引
- 第四步：提取种子的校验和
- 第五步：计算校验和
- 第六步：验证校验和

## 4.BIP44 路径

```
m/44'/coin_type'/account'/change/address_index
```

## 5.门限共享秘密拆分过程

- 关键点：门限共享秘密拆分是将一个秘密分成多份，只有当足够多的份（达到门限）组合起来才能恢复原秘密，常用 Shamir 的秘密共享方案。
#### 门限共享秘密拆分的定义

- 秘密-----> n 份，k 份可恢复， 任意 k 份恢复出这个秘密，分别用门限共享算法和逆门限共享秘密算法

门限共享秘密拆分是指将一个秘密 S 拆分成 n 个份额（shares），使得只有当至少 k 个份额（k 为门限）组合起来时，才能准确恢复原秘密 S，而少于 k 个份额无法获得任何关于 S 的信息。这被称为 (k, n) 门限方案。例如，在一个 (2,3) 方案中，秘密被分成 3 份，任何 2 份能恢复秘密，但 1 份无法。

最常见的方法是 Shamir 的秘密共享方案，基于多项式插值和有限域的数学性质。

#### Shamir 的秘密共享方案利用 k-1 次多项式来实现门限共享，具体步骤如下：
1. 选择一个大于秘密的质数 p。
2. 将秘密 S 设为多项式的常数项，随机生成 k-1 个系数，构成一个 k-1 次多项式 f(x)。
3. 为每个参与者分配一个唯一标识 x_i（通常从 1 到 n），计算他们的份额 y_i = f(x_i) mod p。
4. 每个参与者得到一个份额 (x_i, y_i)。

#### 恢复过程
- 任何 k 个参与者可以用他们的份额通过拉格朗日插值法恢复多项式，计算 f(0) 得到原秘密 S。
#### 以下是门限共享秘密拆分的应用场景对比：
|   场景    |    适用性     |      优势       |
|:-------:|:----------:|:-------------:|
| 分布式密钥管理 |  高，适合多方合作  | 防止单点故障，增强安全性  |
|  数据备份   | 高，适合重要数据分发 | 防止数据泄露，需多方恢复  |
|  访问控制   |  高，适合权限分配  | 灵活控制访问，满足门限要求 |

## 5.MPC 算法 Keygen 和 Sign 分别需要经过多少轮共识

GG18

- Keygen: 5 轮
- Sign: 9 轮

GG20

- Keygen: 5 轮
- Sign: 7 轮

## 6.为什么 schnorr 比特币手续费可以降低

schnorr 签名聚合成一个签名，还有短密钥，这样签名的数据会比 ECDSA 短很多，所以相对于 ECDSA 签名的交易会便宜很多

## 7. 为什么比特币早期时候不直接用 schnorr 签名算法

因为当时 schnorr 算法在专利期

## 8. 相比较之下 EDDSA 性能，安全性都会高一些，为什么比特币以太坊用了 ECDSA，没有用 EDDSA

- ECDSA 是基于更早的标准（如 FIPS 186-4 和 ANSI X9.62）发展的，因此在密码学界和工业界有较长的使用历史和广泛的标准化支持。它被大量系统和协议（如
  TLS 和 Bitcoin）采用，形成了一个庞大的生态系统。
- 虽然 EdDSA 有一些优势，如不容易受到侧信道攻击的影响（如时间攻击和缓存攻击），但 ECDSA
  的安全性也已经过广泛的研究和验证。对于很多开发者和企业来说，使用一个已被长期验证的算法是更为保守和安全的选择。
- EdDSA 通常具有更高的签名速度和较快的验证速度，尤其是在大多数软件实现中。然而，对于已经高度优化的 ECDSA
  实现，性能差异在许多应用中可能并不明显。
- EdDSA 的设计更为简单且更不易出错，特别是在处理随机数生成等方面。然而，ECDSA 由于使用历史更长，开发者更为熟悉其使用和管理。

## 9.中心化钱包开发里面的充值，提现，归集，转冷，冷转热开发业务流程描述

- 💡💡接口返回的 to 是交易所系统里面的用户地址，这笔交易为充值交易；
- 💡💡接口返回的 from 是交易所系统里面的用户地址，这笔交易为提现交易；
- 💡💡接口返回的 to 是交易所的归集地址，from 是系统的用户地址，这笔交易资金归集交易；
- 💡💡接口返回的 to 地址是冷钱包地址，from 地址时热钱包地址，这笔交易热转冷的交易。
- 💡💡接口返回的 from 地址是冷钱包地址，to 地址时热钱包地址，这笔交易冷转热的交易。

### 充值

- 获得最新块高；更新到数据库
- 从数据库中获取上次解析交易的块高做为起始块高，最新块高为截止块高，如果数据库中没有记录，说明需要从头开始扫，起始块高为 0；
- 解析区块里面的交易，to 地址是系统内部的用户地址，说明用户充值，更新交易到数据库中，将交易的状态设置为待确认。
- 所在块的交易过了确认位，将交易状态更新位充值成功并通知业务层。
- 解析到的充值交易需要在钱包的数据库里面维护 nonce, 当然也可以不维护，签名的时候去链上获取

### 提现

- 获取离线签名需要的参数，给合适的手续费
- 构建未签名的交易消息摘要，将消息摘要递给签名机签名
- 构建完整的交易并进行序列化
- 发送交易到区块链网络
- 扫链获取到交易之后更新交易状态并上报业务层

### 归集

- 将用户地址上的资金转到归集地址，签名流程类似提现
- 发送交易到区块链网络
- 扫链获取到交易之后更新交易状态

### 转冷

- 将热钱包地址上的资金转到冷钱包地址，签名流程类似提现
- 发送交易到区块链网络
- 扫链获取到交易之后更新交易状态

### 冷转热

- 手动操作转账到热钱包地址
- 扫链获取到交易之后更新交易状态

## 10.有用过 rosetta api, 请简单描述起作用，并举几个钱包常用的接口说明

Bitcoin Rosetta API 是由 Coinbase 提出的 Rosetta
标准的一部分，旨在为区块链和钱包提供一个统一的接口标准。这个标准化的接口使得与各种区块链的交互更加容易和一致，无论是对交易数据的读取还是写入。目前已经支持很多链，包含比特币，以太坊等主流链，也包含像
IoTex 和 Oasis 这样的非主流链。

### 用到的接口

- /network/list：返回比特币主网和测试网信息。
- /network/status：返回当前最新区块、已同步区块高度、区块链处理器的状态等。
- /block 和 /block/transaction：返回区块和交易的详细信息，包括交易的输入输出、金额、地址等。
- /account/balance：通过查询比特币节点，返回指定地址的余额。

### 发送交易到区块链网络

- /construction/submit：通过比特币节点提交签名后的交易。

### 以下是`Rosetta API`交易流程的表格化表示：

|   步骤    |            	端点            |        	输入         |      	输出      |    	备注     |
|:-------:|:-------------------------:|:------------------:|:-------------:|:----------:|
|  检查余额   |    	/account/balance	     |       账户地址	        |     余额数组      | 	确保有足够 ICP |
|  定义操作	  |            -	             |         -	         |     操作数组	     |    手动构造    |
|  预处理交易  | 	/construction/preprocess |     	网络标识符、操作	     | options, 所需公钥 |  	验证操作合法性  |
| 获取元数据	  |  /construction/metadata	  |   网络标识符、options    |   	metadata   |  	获取动态数据   |
| 生成未签名交易 | 	/construction/payloads	  | 网络标识符、操作、metadata	 |  未签名交易、签名负载	  |    准备签名    |
|  离线签名   |            	-             |     	签名负载、私钥	      |     签名结果	     |   可离线完成    |
| 组合签名交易	 |  /construction/combine	   |   网络标识符、未签名交易、签名   |    	签名交易	     |    合并签名    |
|  提交交易	  |   /construction/submit    |    	网络标识符、签名交易     |    	交易哈希	     |    提交网络    |
|  等待确认   |      	/transaction	       |    网络标识符、交易哈希	     |  交易详情（含区块信息）  | 	检查是否被区块包含 |

## 11.ETH2.0 的 epoch, slot 和 block 简述

### Slot（时隙）

定义：Slot 是以太坊2.0中最基本的时间单位，每个slot都有一个指定的时间长度。在每个 slot 中，可能会有一个区块被提议并添加到链中。
时间长度：一个 slot 的长度为 12 秒。这意味着每 12 秒会有一个新的 slot。
功能：在每个 slot 中，网络中的验证者将有机会提议一个新的区块。这些提议者是通过权益证明（PoS）随机选择的。

### Epoch（纪元）

定义：Epoch 是由多个连续的slot组成的更长时间段。在 Eth2.0 中，Epoch 用于管理和组织验证者的活动。
组成：一个 Epoch 由 32 个 slot 组成。
时间长度：由于一个 slot 是12秒，一个 Epoch 的总长度是 384 秒（即6.4分钟）。
功能：Epoch 是用来实现共识机制的一部分。在每个 Epoch 结束时，网络会进行状态和共识的检查和调整，包括对验证者的奖励和惩罚。

### Block（区块）

定义：Block 是包含交易和其他相关数据的记录单元。在以太坊2.0中，每个slot可能会有一个区块被提议，但不保证每个 slot 都有区块。
内容：一个区块包含区块头、交易列表、状态根哈希、签名等数据。
创建过程：在每个 slot 开始时，网络会随机选出一个验证者来提议区块。该验证者将创建一个包含新交易和其他必要信息的区块，并广播到网络中。

## 12.中心化钱包开发中为什么需要确认位，您怎么理解这个确认位的

在确认位过了之后交易回滚的难度很大，每条链不一样，根据历史和经验来定，以太坊的话可以参照区块状态来做

## 13.简单描述以太坊交易类型，并说明这个交易类型的作用

- legacy: 历史遗留交易类型，签名体为 `gasLimit` 和 `gasPrice`
- EIP1559: `BaseFee` 和 `MaxPriorityFee` 类型
- EIP4844: `blob` 交易类型
- EIP2930: `Access List` 类型

## 14.去中心化和中心化钱包开发中的异同点有哪些？

### 密钥管理方式不同

HD 钱包私钥在本地设备，私钥用户自己控制
交易所钱包中心化服务器(CloadHSM, TEE 等)，私钥项目方控制

### 资金存在方式不同

HD 资金在用户钱包地址
交易所钱包资金在交易所热钱包或者冷钱包里面

### 业务逻辑不一致

中心化钱包：实时不断扫链更新交易数据和状态
HD 钱包：根据用户的操作通过请求接口实现业务逻辑

## 15. 发生硬分叉时，做为钱包的开发，您应当怎么去处理这种状况， 以 ETHPOW 和 ETH2.0 分叉这个过程

硬分叉处理有**4个关键步骤**：

**步骤1 - 提前准备**：
- 监控社区分叉讨论和时间节点
- 准备多套节点环境（主链+分叉链）
- 备份用户资产快照

**步骤2 - 分叉时刻处理**：
- 暂停充值提现服务
- 等待网络稳定和共识确定
- 确认哪条链是主链

**步骤3 - 双链支持**：
- 同时支持两条链的资产查询
- 用户可以选择在哪条链上操作
- 分别维护两套账本系统

**步骤4 - 长期策略**：
- 根据市场和社区共识选择主要支持的链
- 逐步下线不活跃的分叉链
- 给用户足够时间转移资产

**ETHPOW vs ETH2.0案例**：
- ETH2.0成为主链（市值和生态支持）
- ETHPOW作为分叉链继续存在
- 用户在分叉前的ETH在两条链上都有

**记忆要点**：提前准备、暂停服务、双链支持、长期选择

**总结**：硬分叉时要暂停服务观察，然后同时支持两条链，最后根据市场共识决定主要支持哪条。

## 16.TON 支持合约吗？若支持，请说出其合约开发语言

是的，TON（The Open Network）支持智能合约。TON 的设计目标之一就是提供一个高性能、可扩展的区块链平台，能够支持复杂的去中心化应用（DApps）和智能合约。

### TON 的智能合约开发可以使用以下几种语言：

- FunC
    - 这是 TON 官方支持的主要合约开发语言，类似于 C 语言，语法较为底层，上手难度较高。目前 TON
      生态中的主流应用，如钱包和去中心化交易所（DEX），大多使用 FunC 编写。它适合需要深入控制合约逻辑的开发者。
- Tact
    - 由社区支持的高级语言，语法类似于 Solidity，易于上手，适合初学者或希望快速开发简单合约的开发者（如 Token 或
      NFT）。不过，Tact 目前仍在快速发展中，功能可能不够完善，例如不支持合约升级，Gas 费用也较高。
- Tolk
    - TON 官方最新推出的语言，旨在未来逐步替代 FunC。Tolk 相较 FunC 更简洁，且官方计划将所有文档和示例逐步迁移至
      Tolk。目前它处于早期阶段，文档和生态支持还在完善中。
- Fift
    - 一种底层语言，类似于汇编语言，主要用于 TON 虚拟机（TVM）的低级操作。一般开发者很少直接使用，更多用于特定场景或调试。

### 对于大多数开发者来说：

- 如果是简单项目（如发行代币或 NFT），Tact 是快速上手的选择。
- 如果需要深入开发复杂逻辑或与现有主流合约交互，FunC 是当前最成熟和广泛使用的语言。
- Tolk 则是面向未来的选择，适合关注长期发展的开发者。

## 17.比特币的地址有哪些格式，请说明

比特币地址的格式主要基于其前缀和编码方式，可以分为以下几类：

1. Legacy地址（P2PKH）
    - 前缀：以“1”开头。
    - 示例：1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2。
    - 描述：这是比特币最初的地址格式，基于Pay-to-Public-Key-Hash（P2PKH），要求接收者使用对应的私钥签名以证明所有权。
    - 特点：交易费用较高，二维码和手动输入较不方便，适合与不支持SegWit的老式钱包交互。
2. P2SH地址
    - 前缀：以“3”开头。
    - 示例：3J98t1WpEZ73CNmQviecrnyiWrnqRhWNLy。
    - 描述：Pay-to-Script-Hash（P2SH）地址用于更复杂的脚本支付，例如多签地址或嵌套SegWit地址。
    - 特点：支持复杂的交易条件，广泛用于交易所和第三方钱包，但兼容性较Legacy地址稍好。
3. SegWit地址（Bech32）
    - 前缀：以“bc1”开头。
    - 子类型：
        - P2WPKH地址：通常以“bc1q”开头，例如：bc1qar0srrr7xfkvy5l643lydnw9re59gtzzwf5mdq。
        - P2WSH地址：以“bc1”开头且地址较长，例如：bc1qrp33g0q5c5txsp9arysrxwj7a0r24kheu37q2vc9iam624dmybsceqc2f（52字符）。
    - 描述：SegWit（隔离见证）是比特币的一个升级，旨在提高交易效率和降低费用。Bech32编码使用32个字符（小写字母和数字），包含错误纠正码，能检测大部分输入错误。
    - 特点：P2WPKH适合标准公钥哈希支付，P2WSH适合脚本哈希支付，交易费用较低，抗输入错误能力强。
4. Taproot地址（Bech32m）
    - 前缀：通常以“bc1p”开头，例如：bc1p5d7rjq7g6rdk2yhzks9smlaqtedr4dekq08ge8ztwac72sfr9rusxg3297。
    - 描述：Taproot是2021年引入的SegWit版本1升级，使用Bech32m编码（Bech32的修改版本），旨在提高隐私和灵活性，支持Schnorr签名和更复杂的智能合约。
    - 特点：提供更好的安全性和较低的费用，多签交易在外观上与单签交易无异，增强了隐私保护。目前采用率逐渐提高，但并非所有钱包均支持。

- 地址格式的演进与对比
- 比特币地址格式的演进反映了网络的扩展需求和安全性提升：
    - Legacy地址：最早的格式，适合早期用户，但交易大小较大，费用较高。
    - P2SH地址：引入了对复杂脚本的支持，兼容性较好，常用在多签或嵌套SegWit场景。
    - SegWit地址：通过隔离见证减少了交易数据量，降低了费用，Bech32编码提高了用户友好性。
    - Taproot地址：作为SegWit的进一步优化，结合Bech32m编码，显著提升了隐私和智能合约能力。

### 以下是各格式的对比表：

|      格式类型       |  前缀  |                              示例地址                              |           特点            |
|:---------------:|:----:|:--------------------------------------------------------------:|:-----------------------:|
| Legacy (P2PKH)  |  1   |               1BvBMSEYstWetqTFn5Au4m4GFg7xJaNVN2               |     原始格式，费用高，适合老式钱包     |
|      P2SH       |  3   |               3J98t1WpEZ73CNmQviecrnyiWrnqRhWNLy               |       支持复杂脚本，兼容性好       |
| SegWit (P2WPKH) | bc1q |           bc1qar0srrr7xfkvy5l643lydnw9re59gtzzwf5mdq           |  标准SegWit支付，费用低，抗输入错误   |
| SegWit (P2WSH)  | bc1  | bc1qrp33g0q5c5txsp9arysrxwj7a0r24kheu37q2vc9iam624dmybsceqc2f  |       脚本哈希支付，地址较长       |
| Taproot (P2TR)  | bc1p | bc1p5d7rjq7g6rdk2yhzks9smlaqtedr4dekq08ge8ztwac72sfr9rusxg3297 | 隐私增强，支持Schnorr签名，采用率提升中 |

## 18.描述一些 UTXO 和账户模型的区别

- 账户模型：
    - 账户模型用于像以太坊这样的区块链。每个账户有一个余额，交易时直接从你的余额中扣除，添加到接收者的余额，就像银行转账。
- UTXO:
    - UTXO 模型用于像比特币这样的区块链。每个交易有输入和输出，输入是之前未花费的交易输出（UTXO），输出是新的
      UTXO。就像你有几张钞票，要买东西时用掉一些，可能还会有找零。

### 主要区别

- 交易处理：UTXO 需要消耗整个 UTXO 并可能创建找零；账户模型直接调整余额，无需找零。
- 状态管理：UTXO 区块链跟踪所有 UTXO；账户区块链跟踪账户余额。
- 隐私：UTXO 链隐私更好，因为交易不直接与账户关联；账户链交易与特定账户相关联。
- 复杂性：账户链更易支持复杂智能合约；UTXO 链脚本能力较简单。

### 以下是两者的关键差异，整理为表格形式：

|  方面  |                                UTXO模型                                |                          账户模型                          |
|:----:|:--------------------------------------------------------------------:|:------------------------------------------------------:|
|  定义  |  使用未花费交易输出记账方法，无账户/钱包概念，源于比特币白皮书 (https://bitcoin.org/bitcoin.pdf)。  |              类似银行账户的余额管理系统，如以太坊、EOS、Tron。              |
| 示例链  |               比特币、比特币现金、Zcash、Litecoin、Dogecoin、Dash。                |                  以太坊、EOS、Tron、以太坊经典。                   |
| 交易过程 |                消耗现有 UTXO 创建新 UTXO，UTXO 必须整体消耗（如现金钞票）。                | 可部分花费余额，无需找零。例如，从 100 ETH 发送 37.5 ETH，余额直接变为 62.5 ETH。 |
| 交易示例 | 发送 3.75 BTC，输入一个 10 BTC UTXO：接收者得 3.75 BTC，发送者得 6.25 BTC 找零（新 UTXO）。 |      发送 37.5 ETH：发送者余额变为 62.5 ETH，接收者得 37.5 ETH。       |
|  效率  |                 效率较低；需跟踪所有 UTXO 计算余额，对 dApp 开发挑战较大。                  |                  效率较高；只需检查发送者余额是否足够。                   |
| 隐私性  |                       隐私性较好，交易不直接与账户关联，通过地址管理。                       |             隐私性较差，所有交易与特定账户关联，除非使用额外隐私工具。              |
| 复杂性  |                     脚本能力有限，适合简单货币交易，难以支持复杂智能合约。                      |                 支持图灵完备智能合约，适合 dApp 开发。                 |

## 20.解释一下什么是 EVM 同源链，举例说明一下

1. EVM 同源链是指能够运行以太坊虚拟机（EVM）智能合约的区块链，开发者可以用 Solidity 编写合约并部署到这些链上，体验与以太坊类似。
2. 这些链允许开发者复用以太坊生态的工具和应用，降低开发成本，同时通常提供更低的交易费用和更高的性能。
3. 一些 EVM 同源链如 Avalanche C-Chain 和 Polygon，不仅支持以太坊的智能合约，还通过自己的扩展提升了交易速度和隐私保护。

### 以下是几个知名的 EVM 同源链及其特点，整理如下表：

|        链名         |      类型      |              特点              |           示例用例            |
|:-----------------:|:------------:|:----------------------------:|:-------------------------:|
|      Polygon      |    二层扩展方案    |    低交易费用，高吞吐量，PoS桥接以太坊主网     | NFT 市场（如 OpenSea）、DeFi 协议 |
|     BNB Chain     |     独立公链     |    高性能，低费用，支持 Binance 生态     |       去中心化交易所、稳定币发行       |
| Avalanche C-Chain |  子网（EVM 兼容）  |       快速交易，低延迟，支持多子网架构       |       DeFi 应用、跨链桥接        |
|      Fantom       |     独立公链     | 使用 Lachesis 共识，快速确认，低 gas 费用 |       DeFi 协议、链上治理        |
|     Arbitrum      | 二层扩展方案（乐观卷积） |     低费用，继承以太坊安全性，适合高频交易      |         链上游戏、支付应用         |
|     Optimism      | 二层扩展方案（乐观卷积） |     低 gas 费用，专注于以太坊生态扩展      |       DeFi 协议、链上社交        |

## 21.ERC721 和 ERC1155 区别与联系

- ERC721 和 ERC1155 的区别：ERC721 专为非同质化代币（NFT）设计，每个代币都是独特的；ERC1155 可以管理同质化代币和非同质化代币，更加灵活，支持批量操作。
- ERC721 和 ERC1155 的联系：ERC1155 可以创建非同质化代币，包含了 ERC721 的功能，是更全面的标准。

### 以下是 ERC721 和 ERC1155 的详细对比，整理为表格形式：

|  方面  |                         ERC721                          |                       ERC1155                       |
|:----:|:-------------------------------------------------------:|:---------------------------------------------------:|
| 代币类型 |                 仅支持非同质化代币（NFT），每个代币唯一。                  |          支持同质化、非同质化和半同质化代币，可在一个合约中管理多种类型。           |
| 批量操作 | 不支持批量转移，需要多次调用 transferFrom 或 safeTransferFrom，gas 成本高。 | 支持批量转移（如 safeBatchTransferFrom），一次交易可处理多个代币，节省 gas。 |
|  用途  |                适合仅需管理独特资产的项目，如数字艺术品或收藏品。                |            适合需要管理多种代币类型的项目，如游戏生态或资产管理平台。            |
|  效率  |                    效率较低，适合简单 NFT 应用。                    |                效率更高，适合复杂场景，减少合约部署成本。                |
| 批准机制 |               使用 approve 函数，针对特定代币设置批准地址。               |     使用 setApprovalForAll，允许另一个地址管理所有代币，方便市场操作。      |

## 22. Cosmos 共识算法是什么

Cosmos用的是**Tendermint共识**，这个算法有3个核心特点：

**特点1 - BFT共识**：
- 拜占庭容错，最多容忍1/3节点作恶

**特点2 - 即时确定性**：
- 不像比特币要等6个确认，Cosmos交易一旦确认就是最终确认

**特点3 - 高性能**：
- 每秒可以处理几千笔交易

**记忆数字**：1/3容错率
**记忆关键词**：即时确定性

**总结**：Tendermint就是快速确认的BFT共识，记住1/3容错这个关键数字就行。

## 23. Cosmos 钱包资金精度

Cosmos生态的资金精度有**2个层次**：

**层次1 - 基础精度**：
- 大部分Cosmos链：6位小数精度
- 如ATOM代币：1 ATOM = 1,000,000 uatom

**层次2 - 特殊情况**：
- 部分链可能是18位精度（类似以太坊）
- 具体要看各链的genesis配置

**记忆数字**：6位是主流，18位是特例
**记忆关键词**：uatom（微原子）

**总结**：Cosmos主要是6位精度，记住uatom这个最小单位就够了。

## 24. Cosmos 签名结构中的 account_number 和 seqence 怎么获取

获取这**2个关键参数**有标准流程：

**参数1 - account_number**：
- 获取方式：调用 `/cosmos/auth/v1beta1/accounts/{address}` 接口
- 特点：账户创建后就固定不变
- 作用：唯一标识账户

**参数2 - sequence**：
- 获取方式：同样是上面那个接口返回
- 特点：每发送一笔交易就+1
- 作用：防止重放攻击

**记忆要点**：
- 1个接口获取2个参数
- account_number不变，sequence会变

**总结**：记住一个接口地址就够了，两个参数都能拿到，sequence记得每次交易后要更新。

## 25. memo 是什么，在中心化钱包开发中有什么作用，请举例说明那些链带有 Memo, Memo 另一个名字是什么

Memo就是交易的**备注字段**，在中心化钱包中有3个重要作用：

**作用1 - 用户识别**：
- 交易所给每个用户分配唯一memo
- 用户充值时必须填写memo来标识身份

**作用2 - 资金归属**：
- 没有memo的充值无法识别是哪个用户的
- 避免资金混乱

**作用3 - 自动处理**：
- 系统根据memo自动给对应用户上账

**支持Memo的链**：
- Cosmos系列（ATOM、OSMO等）
- Stellar（XLM）
- EOS系列
- Ripple（XRP）

**Memo的别名**：
- Tag（如XRP中叫Destination Tag）
- Message（有些链叫Message）

**总结**：Memo就是交易备注，主要用来在共享地址的情况下区分不同用户，记住XRP叫Tag就行。

## 26. 简述 Cosmos 的 Interchain Security 和 IBC Protocol

这是Cosmos生态的**2大核心技术**：

**技术1 - Interchain Security（共享安全）**：
- 核心思想：小链借用大链的安全性
- 工作原理：Cosmos Hub的验证者同时验证消费链
- 好处：新链不需要自己找验证者，直接享受Hub的安全

**技术2 - IBC Protocol（跨链通信）**：
- 核心思想：不同链之间可以安全通信
- 工作原理：通过轻客户端验证对方链的状态
- 好处：资产可以在不同链之间自由转移

**记忆要点**：
- ICS = 安全共享
- IBC = 跨链通信

**总结**：ICS让小链蹭大链的安全，IBC让不同链能互相转账，这就是Cosmos万链互联的基础。

## 27. cosmos-sdk 的应用场景

Cosmos SDK是**区块链开发框架**，有4大主要应用场景：

**场景1 - 应用链开发**：
- 为特定应用定制专门的区块链
- 如Terra（稳定币）、Osmosis（DEX）

**场景2 - DeFi协议**：
- 去中心化交易所、借贷协议
- 性能比以太坊更好

**场景3 - 游戏链**：
- 游戏专用区块链
- 高TPS，低手续费

**场景4 - 企业级应用**：
- 供应链管理、数字身份
- 可定制化程度高

**记忆关键词**：应用链、DeFi、游戏、企业
**核心优势**：模块化、可定制

**总结**：Cosmos SDK就是搭积木式造链工具，想做什么链都能快速搭建出来。

## 28. solana 交易签名有有效期说法吗？若有情描述什么场景的签名会出现这种状况，怎么去解决？

Solana交易确实有**有效期限制**，主要体现在2个方面：

**限制1 - Blockhash有效期**：
- 有效时间：大约150个slot（约75秒）
- 原因：交易必须引用最近的blockhash

**限制2 - 网络拥堵影响**：
- 正常情况：几秒内确认
- 拥堵时：可能超时失效

**常见失效场景**：
- 离线签名后延迟发送
- 网络拥堵导致交易排队
- 批量交易处理时间过长

**解决方案3步法**：
1. **实时获取**：签名前获取最新blockhash
2. **快速发送**：签名后立即广播
3. **重试机制**：失效后重新签名发送

**记忆数字**：150个slot = 75秒有效期

**总结**：Solana交易有75秒有效期，记住签名后要马上发送，不能拖延。

## 29. 怎么去检验账号处于 active？

检验账号是否active有**3种主要方法**：

**方法1 - 余额检查**：
- 调用getBalance接口
- 有余额 = active，无余额可能是inactive

**方法2 - 账户信息查询**：
- 调用getAccountInfo接口
- 返回null = 不存在，有数据 = active

**方法3 - 交易历史检查**：
- 查询最近交易记录
- 有交易活动 = active

**不同链的特殊情况**：
- Solana：未初始化账户返回null
- Ethereum：空账户余额为0但仍存在
- Bitcoin：UTXO模型，看是否有未花费输出

**记忆要点**：余额、账户信息、交易历史

**总结**：最简单就是查余额和账户信息，两个接口调用就能判断账号是否活跃。

## 30. solana 代币精度

Solana代币精度有**2个层次**：

**层次1 - SOL原生代币**：
- 精度：9位小数
- 最小单位：1 lamport = 0.********* SOL
- 1 SOL = 10^9 lamports

**层次2 - SPL代币**：
- 精度：可自定义（通常6-9位）
- 常见精度：USDC是6位，大部分代币是9位
- 在mint账户中定义

**获取精度的方法**：
- 调用getMint接口
- 查看decimals字段

**记忆数字**：
- SOL = 9位精度
- USDC = 6位精度
- 其他代币 = 看mint信息

**总结**：SOL固定9位精度，其他代币要查mint信息，记住lamport这个最小单位。

## 31. 什么是 EVM 同源链，EVM 同源链钱包和 Ethereum 钱包开发中有什么区别

EVM同源链就是**兼容以太坊虚拟机的区块链**，开发差异主要体现在3个方面：

**相同点（90%一样）**：
- 地址格式：都是0x开头的40位十六进制
- 签名算法：都用secp256k1
- 交易结构：基本相同
- 智能合约：Solidity代码可直接部署

**差异点1 - 网络参数**：
- ChainID不同：ETH是1，BSC是56，Polygon是137
- RPC端点不同
- 区块时间不同

**差异点2 - Gas机制**：
- Gas价格：EVM同源链通常更便宜
- Gas限制：可能有不同的默认值

**差异点3 - 特殊功能**：
- 有些链有额外的预编译合约
- 共识机制可能不同（PoS vs PoA）

**记忆要点**：90%相同，主要差异在网络配置

**总结**：EVM同源链开发基本和以太坊一样，只需要改改ChainID和RPC地址就行。

## 32. Cosmos 新版本和老版本签名异同点

Cosmos签名在不同版本间有**3个主要变化**：

**变化1 - 签名算法**：
- 老版本：主要用secp256k1
- 新版本：支持多种算法（ed25519、secp256k1等）

**变化2 - 交易格式**：
- 老版本：amino编码
- 新版本：protobuf编码（更高效）

**变化3 - 签名模式**：
- 老版本：SIGN_MODE_LEGACY_AMINO_JSON
- 新版本：SIGN_MODE_DIRECT（protobuf直接签名）

**兼容性处理**：
- 新版本仍支持老格式
- 建议使用新格式提高效率

**记忆要点**：
- 编码：amino → protobuf
- 模式：LEGACY → DIRECT

**总结**：主要是从amino编码升级到protobuf编码，新版本更高效但保持向后兼容。

## 33. 简要说明 EOS 账户的激活过程

EOS账户激活需要**3个必要条件**：

**条件1 - 账户名称**：
- 12位字符，只能用a-z和1-5
- 如：myeosaccount

**条件2 - 密钥对**：
- Owner密钥：最高权限
- Active密钥：日常操作权限

**条件3 - 资源抵押**：
- 需要抵押EOS获取CPU和NET资源
- 最少需要几个EOS才能正常使用

**激活流程4步**：
1. 生成密钥对
2. 选择账户名
3. 找已有账户帮忙创建（需要消耗创建者的资源）
4. 抵押EOS获取资源

**记忆要点**：12位名称、2个密钥、资源抵押

**总结**：EOS账户不是免费的，需要别人帮忙创建并抵押资源，这是和其他链最大的区别。

## 34. 和 EOS 同源的有哪些链

EOS同源链主要有**4条主要分叉链**：

**分叉链1 - WAX**：
- 专注：游戏和NFT
- 特点：免费交易，游戏生态丰富

**分叉链2 - Telos**：
- 特点：高性能，低延迟
- 改进：更好的治理机制

**分叉链3 - Ultra**：
- 专注：游戏平台
- 特点：游戏发行和交易

**分叉链4 - Proton**：
- 专注：支付和身份验证
- 特点：用户友好的账户系统

**共同特征**：
- 都使用EOSIO软件
- 账户名都是12位字符
- 都需要资源抵押

**总结**：EOS的几个分叉链都专注不同领域，但底层技术基本相同，开发经验可以互通。

## 35. SUI 链的特点

SUI链有**4个核心特点**：

**特点1 - 对象模型**：
- 不同于账户模型，每个资产都是独立对象
- 对象有唯一ID和所有者

**特点2 - 并行执行**：
- 无关联交易可以并行处理
- 大幅提升TPS性能

**特点3 - Move语言**：
- 专为资产安全设计的智能合约语言
- 资源不能被复制或丢失

**特点4 - 低延迟确认**：
- 简单交易可以快速确认
- 复杂交易走共识流程

**记忆关键词**：
- 对象模型、并行执行、Move语言、快速确认

**性能数据**：
- 理论TPS：10万+
- 确认时间：亚秒级

**总结**：SUI最大特点就是对象模型和并行执行，用Move语言保证资产安全，性能很强。

## 36. Tron 签名和 EVM 链的不同点（和 legacy 交易类型相比较）

Tron和EVM链签名有**3个主要差异**：

**差异1 - 序列化格式**：
- EVM链：RLP编码
- Tron：Protobuf编码

**差异2 - 哈希算法**：
- EVM链：Keccak256
- Tron：SHA256

**差异3 - 签名恢复**：
- EVM链：v值包含chainId信息（v = recovery_id + 35 + chainId * 2）
- Tron：v值简单（27或28）

**相同点**：
- 都使用secp256k1椭圆曲线
- 都生成r、s、v三个值
- 地址生成方式类似

**记忆要点**：
- 编码：RLP vs Protobuf
- 哈希：Keccak256 vs SHA256
- v值：复杂 vs 简单

**总结**：Tron用Protobuf和SHA256，比EVM链的RLP和Keccak256更标准化一些。

## 37. KDA 由多少条链组成，账户这些链之后有关联吗

KDA（Kadena）采用**多链并行架构**：

**链数量**：
- 当前：20条并行链（Chain 0 到 Chain 19）
- 可扩展：理论上可以扩展到更多链

**账户关联性**：
- 同一个账户可以在所有链上存在
- 每条链上的余额是独立的
- 需要跨链转账来移动资金

**工作原理**：
- 20条链同时出块
- 每条链处理不同的交易
- 通过跨链机制实现资金转移

**记忆数字**：20条链并行运行

**优势**：
- 吞吐量是单链的20倍
- 安全性保持不变

**总结**：KDA有20条并行链，同一账户在每条链上余额独立，需要跨链转账来移动资金。

## 38. KDA 跨链转账的流程描述

KDA跨链转账需要**3个步骤**：

**步骤1 - 发起转账**：
- 在源链（如Chain 0）发起transfer-crosschain交易
- 指定目标链ID（如Chain 1）
- 资金被锁定在源链

**步骤2 - 生成证明**：
- 源链交易确认后生成SPV证明
- 证明包含交易存在性和有效性

**步骤3 - 目标链确认**：
- 在目标链提交SPV证明
- 目标链验证证明有效性
- 在目标链释放对应资金

**关键概念**：
- SPV证明：简化支付验证证明
- 双向确认：源链锁定，目标链释放

**记忆要点**：锁定→证明→释放

**总结**：KDA跨链就是在源链锁钱，生成证明，然后在目标链解锁，整个过程需要两笔交易。

## 39. KDA 共识算法独立吗？矿工奖励有关联吗

KDA的共识机制有**2个层面的设计**：

**共识层面 - 统一协调**：
- 所有链使用相同的Chainweb共识
- 链与链之间相互引用区块头
- 形成一个整体的安全网络

**挖矿层面 - 独立运行**：
- 每条链独立出块
- 矿工可以选择挖任意链
- 难度调整独立进行

**矿工奖励机制**：
- 每条链的奖励独立计算
- 奖励数量相同但来源不同
- 矿工倾向于挖难度低的链

**记忆要点**：
- 共识统一，挖矿独立
- 奖励独立，难度独立

**总结**：KDA共识是统一的但每条链独立挖矿，矿工奖励也是独立的，这样既保证安全又提高效率。

## 40. ERC721 和 ERC1155 区别

ERC721和ERC1155有**4个主要区别**：

**区别1 - 代币类型**：
- ERC721：只支持非同质化代币（NFT）
- ERC1155：支持同质化、非同质化和半同质化代币

**区别2 - 批量操作**：
- ERC721：不支持批量转移，gas成本高
- ERC1155：支持批量操作，一次转移多个代币

**区别3 - 合约架构**：
- ERC721：一个合约管理一种NFT
- ERC1155：一个合约可以管理多种代币类型

**区别4 - 存储效率**：
- ERC721：每个代币需要单独存储
- ERC1155：相同类型代币可以共享存储

**应用场景对比**：
- ERC721：适合独特艺术品、收藏品
- ERC1155：适合游戏道具、多类型资产

**记忆要点**：721单一NFT，1155多类型，1155支持批量更高效

**总结**：ERC721专门做NFT，ERC1155更全能，可以同时管理多种类型的代币，效率更高。

## 41. NFT MINT 流程

NFT铸造有**4个核心步骤**：

**步骤1 - 准备元数据**：
- 上传图片到IPFS或其他存储
- 创建metadata.json文件
- 包含名称、描述、属性等信息

**步骤2 - 部署合约**：
- 部署ERC721或ERC1155合约
- 设置合约参数（名称、符号等）

**步骤3 - 调用mint函数**：
- 指定接收者地址
- 传入tokenId和metadata URI
- 支付gas费用

**步骤4 - 确认上链**：
- 等待交易确认
- NFT正式铸造完成

**记忆要点**：元数据→合约→mint→确认

**成本构成**：
- 存储费用（IPFS）
- 合约部署费用
- mint交易费用

**总结**：NFT mint就是先准备好图片和信息，然后调用合约的mint函数把NFT铸造出来。

## 42. LSD 产品的质押流程（以 lido 为例说明）

Lido质押有**4个简单步骤**：

**步骤1 - 用户质押**：
- 用户发送ETH到Lido合约
- 最低质押金额：无限制（可以质押任意数量）

**步骤2 - 获得stETH**：
- 立即获得等量的stETH代币
- stETH代表质押的ETH份额

**步骤3 - 自动复利**：
- stETH余额每天自动增长
- 增长率等于验证者奖励率

**步骤4 - 随时交易**：
- stETH可以在DEX上交易
- 不需要等待解锁期

**记忆数字**：
- 1:1兑换比例（ETH:stETH）
- 每日自动复利

**优势**：
- 无最低限制
- 流动性好
- 自动复利

**总结**：Lido就是把ETH换成stETH，然后stETH会自动增长，还能随时交易，非常方便。

## 43. Solana 质押流程

Solana质押需要**5个步骤**：

**步骤1 - 创建质押账户**：
- 调用createStakeAccount指令
- 需要支付账户租金（约0.00228 SOL）

**步骤2 - 转入SOL**：
- 将要质押的SOL转入质押账户
- 最低质押：1 SOL

**步骤3 - 委托验证者**：
- 选择验证者节点
- 调用delegate指令

**步骤4 - 激活期等待**：
- 等待1-2个epoch激活（约2-4天）
- 激活后开始获得奖励

**步骤5 - 获得奖励**：
- 每个epoch自动获得奖励
- 奖励直接添加到质押账户

**记忆数字**：
- 最低1 SOL
- 激活期1-2 epoch
- 账户租金0.00228 SOL

**总结**：Solana质押要先创建专门账户，然后委托给验证者，等几天激活后就开始赚奖励。

## 44. Tezos 质押流程

Tezos质押（称为Baking）有**3种参与方式**：

**方式1 - 自己做Baker**：
- 需要：至少8000 XTZ
- 运行：24/7在线的节点
- 奖励：最高，但技术要求高

**方式2 - 委托质押**：
- 需要：任意数量XTZ
- 操作：委托给现有Baker
- 奖励：Baker分成后的收益

**方式3 - 质押池**：
- 需要：少量XTZ即可
- 操作：加入质押池
- 奖励：池子统一分配

**委托流程4步**：
1. 选择可靠的Baker
2. 发送委托交易
3. 等待7个周期激活（约20天）
4. 开始获得奖励

**记忆数字**：8000 XTZ门槛，7个周期激活

**总结**：Tezos质押门槛高，普通用户最好选择委托给Baker，等20天激活后开始赚收益。

## 45.如何计算一笔 BTC 交易的所需要的 gasFee，有什么方案

比特币交易的“gasFee”实际上是指交易费用（transaction fee），以 satoshis 为单位计算，取决于交易大小（以虚拟字节 vB 为单位）和当前每
vB 的费用率。

### 交易费用通过以下公式计算：

交易费用（sat） = 交易大小（vB） × 每 vB 费用率（sat/vB）

- 交易大小（vB）根据交易类型不同而变化，例如标准 P2PKH 交易约 226 vB，SegWit P2WPKH 交易约 140 vB。
- 每 vB 费用率由市场决定，可通过 Bitcoin Core、Blockchain.com 或 BitcoinFees 等工具查询。

|       交易类型       | 典型 vB 大小 |              说明               |
|:----------------:|:--------:|:-----------------------------:|
|   标准 P2PKH 交易    | 约 226 vB |    一个输入、两个输出（一个给接收者，一个找零）。    |
| SegWit P2WPKH 交易 | 约 140 vB |     使用隔离见证，输入和输出较小，费用更低。      |
|   复杂交易（多输入/输出）   |  视情况而定   | 每个额外输入约增加 41-62 vB，输出约 31 vB。 |

### 每 vB 费用率的获取

费用率由市场动态决定，基于当前 mempool（待处理交易池）的拥堵程度。以下是获取费用率的主要方案：

- Bitcoin Core 的 estimatefee 命令：
    - 如果运行 Bitcoin Core 节点，可使用 `estimatefee` 命令，输入期望的确认块数（如 1 块确认），返回建议的费用率。
    - 示例：`estimatefee 1` 返回当前网络中 1 块确认所需的费用率（sat/vB）。
- 第三方服务：
    - 使用网站如 Blockchain.com 或 BitcoinFees，提供实时费用率建议，通常分为高、中、低优先级。
    - 例如，当前高优先级可能为 20 sat/vB，中优先级 10 sat/vB，低优先级 5 sat/vB。
- 钱包工具：
    - 钱包如 Electrum 或 BlueWallet 提供动态费用建议，允许用户选择“快速”、“经济”或自定义费用率。
    - 例如，选择“快速”可能对应 20 sat/vB，确保下一块确认。

### 计算示例

- 假设一个 SegWit P2WPKH 交易，vB 大小为 140，当前费用率为 10 sat/vB:
    - 交易费用 = 140 × 10 = 1400 sat = 0.00001400 BTC
- 再假设一个标准 P2PKH 交易，vB 为 226，费用率为 10 sat/vB:
    - 交易费用 = 226 × 10 = 1400 sat = 0.0.00002260 BTC

可见，SegWit 交易费用更低。

### 以下是计算交易费用的具体方案：

- 手动计算：
    - 确定交易类型，估算 vB 大小（基于输入/输出数量）。
    - 从上述来源获取当前费用率，代入公式计算。
    - 适合技术用户，但需注意 vB 计算的复杂性。
- 钱包自动计算：
    - 大多数比特币钱包（如 Electrum、BlueWallet）自动计算费用，基于用户选择的优先级。
    - 用户可自定义费用率，适合灵活控制。
- 在线计算器：
    - 使用在线工具如 Bitcoin Transaction Fee Calculator，输入交易详情（输入/输出数量、类型），获取费用估算。
    - 适合非技术用户，快速获取结果。
- 动态估算算法：
    - Bitcoin Core 使用基于 mempool 的算法，预测不同确认时间所需的费用率。
    - 第三方服务可能使用中位数或百分位数（如 90% 交易支付的费用率）估算。

### 46.如何计算一笔evm交易的所需要的gasfee，有什么方案

## 47.如何处理BTC的交易执行缓慢，有什么方案，分别有什么区别？

1. 提高交易费用（Replace By Fee, RBF）
    - 如果你是发送方，且钱包支持“替换按费用”（RBF），可以替换原交易，设置更高的费用以加速确认。需要确保原交易未确认且支持RBF。
    - 定义：RBF允许发送方替换未确认交易，新的交易需支付更高费用以提高优先级。
    - 工作原理：原交易需设置RBF标志（opt-in RBF），新交易必须支付更高的总费用，且输出保持相同或更高。矿工会优先处理费用更高的交易。
    - 适用场景：适合发送方，且钱包支持RBF（如Bitcoin Core、Electrum）。
    - 实施步骤：
        1. 确认原交易未确认且支持RBF。
        2. 使用钱包的“增加费用”功能（如Blockstream Green）或手动创建新交易。
        3. 设置更高费用率（如从5 sat/vB提高到20 sat/vB）。
    - 限制：需钱包支持，部分钱包默认关闭RBF；若原交易已确认，RBF无效。
    - 示例：假设原交易费用为1400 sat（140 vB × 10 sat/vB），可替换为费用2260 sat（140 vB × 16 sat/vB）以加速。
2. 使用子支付父（Child Pays for Parent, CPFP）
    - 如果你是接收方，且控制了被卡交易的输出，可以创建一个新交易，花费该输出并支付高费用，激励矿工同时确认两者。适合接收方操作。
    - 定义：接收方创建一个新交易，花费被卡交易的输出，并支付高费用，激励矿工同时确认两者。
    - 工作原理：新交易（子交易）的高费用率使矿工愿意同时确认其依赖的父交易。比特币共识规则要求父交易先于子交易被确认。
    - 适用场景：适合接收方，且控制了被卡交易的输出地址。
    - 实施步骤：
        1. 确认被卡交易输出地址由你控制（如Electrum中查看详情）。
        2. 创建新交易，输入为被卡交易的输出，输出为你的新地址，设置高费用（如20 sat/vB）。
        3. 广播新交易，等待矿工确认。
    - 限制：需有足够余额支付子交易费用；若父交易输出已被花费，CPFP不可用。
    - 示例：若父交易费用低（5 sat/vB），子交易可设20 sat/vB，总费用率提高，矿工更倾向确认。
3. 交易加速服务
    - 通过第三方服务（如ViaBTC或BitAccelerate）提交交易ID，支付费用让矿工优先处理。服务有免费和付费选项，但需注意潜在风险。
    - 定义：第三方服务通过与矿池合作，优先处理用户提交的交易，通常需支付费用。
    - 工作原理：用户提交交易ID（TXID），服务通过多节点重播或直接请求矿池优先处理。部分服务免费，部分收费。
    - 适用场景：当RBF和CPFP不可用时，或用户不熟悉技术操作。
    - 常见服务：
        - ViaBTC：提供免费（限时）和付费服务，付费可加速1000次/小时，费用0.0001 BTC/KB (ViaBTC Transaction Accelerator)。
        - BitAccelerate：免费重播交易至10节点，无需注册 (BitAccelerate)。
        - BTC.com：批处理加速，费用低，但需支付加速费。
    - 限制：效果不确定，可能存在诈骗风险；收费服务需额外成本。
    - 示例：提交TXID至ViaBTC，支付0.0001 BTC/KB，交易可能1小时内确认。
4. 等待网络恢复
    - 如果不急，可以等待网络拥堵缓解，交易自然确认，但可能需要较长时间。
    - 定义：不采取行动，等待网络拥堵缓解，交易自然被矿工确认。
    - 工作原理：比特币每10分钟生成一个块，块大小1MB，拥堵时低费用交易被延迟。网络恢复后，交易可能被处理。
    - 适用场景：不急需资金，且能接受较长等待时间（如周末网络较空闲）。
    - 限制：可能需数小时或数天，交易可能被内存池丢弃。
    - 示例：若当前费用率10 sat/vB，等待至周末费用率降至5 sat/vB，交易可能被确认。

#### 令人惊讶的细节：SegWit的优势

令人惊讶的是，使用SegWit交易因体积小（约140 vB vs 226 vB），费用低，更易被矿工优先确认，适合未来交易预防延迟。

### 以下是各方案的对比表：

|   方案   |  适用角色  |    费用    |    技术要求    | 效果不确定性 |      适用场景      |
|:------:|:------:|:--------:|:----------:|:------:|:--------------:|
|  RBF   |  发送方   | 无额外（原交易） | 中等（需RBF支持） |   低    |  发送方，钱包支持RBF   |
|  CPFP  |  接收方   | 无额外（原交易） | 中等（需控制输出）  |   低    |   接收方，控制输出地址   |
| 交易加速服务 | 发送/接收方 |  可能需付费   | 低（提交TXID）  |  中等至高  | 其他方案不可用，需快速确认  | 
| 等待网络恢复 | 发送/接收方 |    无     |     无      |   高    | 不急需资金，能接受长等待时间 |

- 选择建议：
    - 优先尝试RBF（发送方）或CPFP（接收方），无额外成本，效果较确定。
    - 若无法操作，可选择信誉良好的交易加速服务，但注意费用和风险。
    - 若不急，可等待网络恢复，但需耐心。

## 48. 如何处理EVM的交易执行缓慢，有什么方案，分别有什么区别？

EVM交易缓慢有**4种解决方案**：

**方案1 - 提高Gas价格**：
- 操作：增加gasPrice或maxFeePerGas
- 适用：EIP-1559之前的交易
- 效果：立竿见影，但成本高

**方案2 - Replace交易**：
- 操作：用相同nonce发送新交易，提高gas价格
- 适用：交易还在mempool中
- 效果：替换原交易，快速确认

**方案3 - 使用加速器**：
- 操作：通过第三方服务加速
- 如：Flashbots、1inch等
- 效果：不确定，可能有额外费用

**方案4 - 等待网络恢复**：
- 操作：什么都不做
- 适用：不急的交易
- 效果：免费但耗时

**记忆要点**：提价→替换→加速器→等待

**总结**：EVM交易慢最直接就是加钱提高gas价格，或者用相同nonce替换交易，实在不行就等网络不拥堵。

## 49. 请设计一个合约的多签钱包

多签钱包设计需要**4个核心组件**：

**组件1 - 签名者管理**：
- 存储所有签名者地址
- 设置签名阈值（如3/5多签）
- 支持添加/删除签名者

**组件2 - 交易提案**：
- 任意签名者可以提交交易提案
- 包含目标地址、金额、数据
- 分配唯一的提案ID

**组件3 - 签名收集**：
- 其他签名者对提案进行签名
- 记录每个提案的签名状态
- 防止重复签名

**组件4 - 执行机制**：
- 达到阈值后自动执行交易
- 执行失败的处理机制
- 事件日志记录

**关键函数**：
- submitTransaction()：提交提案
- confirmTransaction()：确认签名
- executeTransaction()：执行交易

**总结**：多签钱包就是需要多个人签名才能花钱的智能合约，核心是提案→签名→达到阈值→执行。

## 50. 你怎么对接的tee？怎么请求的tee？tee之间是如何相互通讯的？

TEE对接有**3个层面的交互**：

**层面1 - 应用层对接**：
- 通过HTTPS API调用TEE服务
- 发送加密的签名请求
- 接收签名结果

**层面2 - 请求流程**：
- 客户端发送签名请求到TEE
- TEE内部验证请求合法性
- 使用安全存储的私钥签名
- 返回签名结果给客户端

**层面3 - TEE间通信**：
- 通过远程证明（Remote Attestation）建立信任
- 使用加密通道传输敏感数据
- 支持密钥分片在多个TEE间分布

**关键技术**：
- Intel SGX或ARM TrustZone
- 远程证明协议
- 端到端加密

**安全保证**：
- 代码和数据在TEE内加密执行
- 外部无法访问TEE内部状态

**总结**：TEE对接就是通过加密API调用，TEE之间通过远程证明建立信任后加密通信，保证私钥安全。

## 51. mpc密钥的安全性保证

MPC密钥安全性有**4层保护机制**：

**保护1 - 密钥分片**：
- 私钥被分成多个片段
- 单个片段无法恢复完整私钥
- 需要达到阈值才能使用

**保护2 - 零知识证明**：
- 签名过程不暴露私钥片段
- 各方只知道自己的片段
- 外部无法获得任何私钥信息

**保护3 - 分布式计算**：
- 签名在多个节点间协作完成
- 没有单点故障风险
- 恶意节点无法破坏系统

**保护4 - 密码学安全**：
- 基于困难数学问题（如离散对数）
- 即使量子计算也难以破解
- 可证明的安全性

**记忆要点**：分片、零知识、分布式、密码学

**安全阈值**：通常是t-of-n方案（如2-of-3）

**总结**：MPC就是把私钥拆分后分布式签名，既保证安全又避免单点风险，是目前最安全的密钥管理方案。

## 52. Solana 代币的特殊性

Solana代币有**3个独特特点**：

**特点1 - ATA账户模型**：
- 每个用户每种代币都需要单独的ATA账户
- 不像EVM链一个地址管理所有代币
- 需要先创建ATA才能接收代币

**特点2 - 账户租金机制**：
- 所有账户都需要支付租金维持存在
- 代币账户也需要租金（约0.002 SOL）
- 余额为0的账户会被回收

**特点3 - 程序派生地址**：
- ATA地址是通过算法确定的
- 基于用户地址+代币mint地址生成
- 保证唯一性和可预测性

**记忆关键词**：
- ATA账户、租金机制、程序派生

**开发影响**：
- 转账前需检查ATA是否存在
- 可能需要先创建ATA账户
- 要考虑租金成本

**总结**：Solana代币最特殊的就是ATA账户模型，每种代币都要单独账户，还要付租金维持。

## 53. aptos代币的特殊性

Aptos代币有**4个独特设计**：

**特点1 - 资源模型**：
- 代币作为资源存储在用户账户下
- 不是余额记录，而是实际的资源对象
- 资源不能被复制或丢失

**特点2 - Move语言特性**：
- 使用Move语言编写代币合约
- 线性类型系统保证资源安全
- 编译时就能防止双花等问题

**特点3 - 账户模型**：
- 每个账户可以存储多种资源
- 不需要像Solana那样创建单独账户
- 但需要先注册代币类型

**特点4 - 并行执行**：
- 支持交易并行处理
- 提高网络吞吐量
- 无关联交易可同时执行

**记忆关键词**：
- 资源模型、Move语言、账户存储、并行执行

**开发差异**：
- 需要理解Move语言概念
- 资源操作而非余额修改

**总结**：Aptos最特殊的是资源模型和Move语言，代币是真实的资源对象，安全性更高。

## 54. 椭圆曲线算法底层实现，以及rsv是什么？分别介绍一下

椭圆曲线签名有**3个核心组件**：

**RSV签名结构**：

**R值（32字节）**：
- 椭圆曲线上一个点的x坐标
- 通过随机数k和基点G计算得出
- R = (k * G).x

**S值（32字节）**：
- 签名的核心部分
- S = k^(-1) * (hash + r * private_key) mod n
- 包含了私钥和消息的信息

**V值（1字节）**：
- 恢复标识符（Recovery ID）
- 用于从签名中恢复公钥
- 通常是27或28（以太坊中包含chainId）

**底层实现流程**：
1. 生成随机数k
2. 计算椭圆曲线点R = k * G
3. 计算s值
4. 确定v值用于公钥恢复

**记忆要点**：R是点坐标，S是签名核心，V是恢复标识

**总结**：RSV就是椭圆曲线签名的三个部分，R和S是数学计算结果，V帮助恢复公钥。

## 55. ecdsa 和 eddsa 区别

ECDSA和EdDSA有**4个主要差异**：

**差异1 - 椭圆曲线类型**：
- ECDSA：使用Weierstrass曲线（如secp256k1）
- EdDSA：使用Edwards曲线（如Ed25519）

**差异2 - 签名过程**：
- ECDSA：需要随机数k，容易受侧信道攻击
- EdDSA：确定性签名，不需要随机数

**差异3 - 性能表现**：
- ECDSA：验证较慢，签名速度一般
- EdDSA：签名和验证都更快

**差异4 - 安全特性**：
- ECDSA：随机数生成不当会泄露私钥
- EdDSA：抗侧信道攻击，更安全

**应用场景**：
- ECDSA：比特币、以太坊等老项目
- EdDSA：Solana、新一代区块链

**记忆要点**：
- ECDSA需要随机数，EdDSA确定性
- EdDSA更快更安全

**总结**：EdDSA是ECDSA的改进版，更快更安全，但ECDSA应用更广泛因为历史原因。

## 56. 签名机有支持HD钱包方式吗？

签名机支持HD钱包有**3种实现方式**：

**方式1 - 种子存储**：
- 在签名机内存储HD钱包种子
- 根据BIP32路径派生私钥
- 支持无限地址生成

**方式2 - 主私钥存储**：
- 存储HD钱包的主私钥
- 实时计算子私钥
- 节省存储空间

**方式3 - 预生成模式**：
- 预先生成一批私钥存储
- 按需使用，用完再生成
- 平衡安全性和性能

**技术实现**：
- 支持BIP32/BIP44标准
- 硬化派生和非硬化派生
- 多币种支持

**安全考虑**：
- 种子的安全存储和备份
- 派生路径的访问控制
- 防止路径遍历攻击

**总结**：现代签名机都支持HD钱包，通常存储种子然后按路径派生私钥，既安全又灵活。

## 57. 签名的安全传输方案

签名安全传输有**4层防护措施**：

**防护1 - 传输加密**：
- 使用TLS/HTTPS加密通道
- 端到端加密保护数据
- 防止中间人攻击

**防护2 - 消息认证**：
- 使用HMAC验证消息完整性
- 防止数据被篡改
- 确保消息来源可信

**防护3 - 时间戳验证**：
- 签名请求包含时间戳
- 防止重放攻击
- 设置合理的时间窗口

**防护4 - 访问控制**：
- API密钥认证
- IP白名单限制
- 请求频率限制

**传输流程**：
1. 客户端加密签名请求
2. 通过HTTPS发送到签名机
3. 签名机验证请求合法性
4. 返回加密的签名结果

**记忆要点**：加密、认证、时间戳、访问控制

**总结**：签名传输要用HTTPS加密，加上消息认证和时间戳，多层防护确保安全。

## 58. BTC和ETH签名的全流程

BTC和ETH签名流程有**5个共同步骤**：

**步骤1 - 构建交易**：
- BTC：构建UTXO输入输出
- ETH：构建账户转账信息

**步骤2 - 序列化交易**：
- BTC：按比特币格式序列化
- ETH：RLP编码序列化

**步骤3 - 计算哈希**：
- BTC：双重SHA256哈希
- ETH：Keccak256哈希

**步骤4 - ECDSA签名**：
- 都使用secp256k1椭圆曲线
- 生成r、s、v三个值

**步骤5 - 组装完整交易**：
- 将签名附加到原始交易
- 广播到对应网络

**主要差异**：
- 序列化格式不同
- 哈希算法不同（SHA256 vs Keccak256）
- v值计算方式不同

**记忆要点**：构建→序列化→哈希→签名→组装

**总结**：BTC和ETH签名流程基本相同，主要差异在序列化格式和哈希算法上。

## 59. 交易里面如何处理合约充值

合约充值处理有**4个关键步骤**：

**步骤1 - 识别合约交易**：
- 检查交易的to地址是否为合约地址
- 通过getCode()判断是否有合约代码

**步骤2 - 解析交易日志**：
- 监听Transfer事件日志
- 解析event中的from、to、value参数
- 确定实际的资金流向

**步骤3 - 验证充值地址**：
- 检查Transfer事件中的to地址
- 确认是否为系统内用户地址
- 区分充值和其他合约调用

**步骤4 - 处理特殊情况**：
- 多层合约调用的处理
- 失败交易的回滚处理
- Gas费用的正确计算

**关键技术点**：
- Event日志解析
- ABI解码
- 内部交易追踪

**记忆要点**：识别合约→解析日志→验证地址→处理异常

**总结**：合约充值主要是解析Transfer事件日志，看to地址是不是我们的用户，比普通转账复杂一些。

## 60. 什么是 Bitcoin 交易延展性，隔离见证是如何消除了交易延展性

交易延展性问题有**2个核心概念**：

**问题定义**：
- 交易内容不变，但交易ID可能改变
- 签名数据的微小修改导致哈希变化
- 影响依赖该交易的后续交易

**延展性原因**：
- 签名中的s值可以有两种等价形式
- DER编码的灵活性
- 签名数据包含在交易哈希计算中

**隔离见证解决方案3步**：

**步骤1 - 分离签名数据**：
- 将签名数据移到witness字段
- 交易ID计算不包含witness数据

**步骤2 - 新的哈希计算**：
- 使用wtxid包含完整交易数据
- txid只包含非witness数据

**步骤3 - 向后兼容**：
- 老节点仍能验证交易
- 新节点获得延展性保护

**记忆要点**：分离签名→新哈希→向后兼容

**总结**：交易延展性就是签名改变导致交易ID变化，隔离见证把签名分离出来单独处理就解决了。

## 61. solana地址大小写敏感怎么处理，比如你发给A的地址却发到了a的地址，有遇到过吗，怎么处理

Solana地址大小写问题有**3个处理策略**：

**策略1 - 预防措施**：
- 输入时统一转换为小写
- 使用地址校验函数验证格式
- 实现地址簿功能避免手动输入

**策略2 - 校验机制**：
- 使用PublicKey.isOnCurve()验证地址有效性
- Base58解码验证地址格式
- 双重确认重要转账

**策略3 - 错误处理**：
- 无效地址会导致交易失败
- 失败交易不会扣除资金
- 只损失少量gas费用

**技术实现**：
```javascript
// 地址标准化
const normalizeAddress = (addr) => {
  return new PublicKey(addr).toString();
}
```

**实际经验**：
- Solana地址是Base58编码，大小写敏感
- 错误地址通常无法通过验证
- 系统应该在发送前验证地址有效性

**总结**：Solana地址大小写敏感，最好的办法是输入时就校验和标准化，避免用户输错。

## 62. solana没有abi 你怎么处理合约交易

Solana没有ABI的情况下有**4种处理方法**：

**方法1 - IDL文件**：
- 使用Anchor框架生成的IDL文件
- IDL包含程序接口定义
- 类似于以太坊的ABI功能

**方法2 - 指令解析**：
- 手动解析指令数据结构
- 根据程序文档理解数据格式
- 使用Borsh序列化标准

**方法3 - 程序客户端库**：
- 使用官方提供的JavaScript/Rust客户端
- 封装了指令构建和解析逻辑
- 如@solana/spl-token库

**方法4 - 交易日志分析**：
- 解析程序日志输出
- 从日志中提取关键信息
- 结合指令数据推断交易内容

**实际应用**：
- SPL Token：有标准接口
- 自定义程序：需要IDL或文档
- 复杂DeFi：通常提供SDK

**记忆要点**：IDL文件、指令解析、客户端库、日志分析

**总结**：Solana虽然没有ABI，但有IDL文件和客户端库，实际开发中并不比以太坊复杂多少。

## 63. 要统计很多代币合约的热度，交易量，用户排行榜等，这个系统等架构数据库啊，技术选型你怎么设计？

代币统计系统需要**4层架构设计**：

**第1层 - 数据采集层**：
- 多节点并行扫链获取交易数据
- 使用Kafka消息队列缓冲数据
- Redis做实时数据缓存

**第2层 - 数据存储层**：
- PostgreSQL存储结构化数据（用户、交易记录）
- ClickHouse存储时序数据（价格、交易量）
- MongoDB存储非结构化数据（合约元数据）

**第3层 - 计算处理层**：
- Spark/Flink做实时流计算
- 定时任务计算排行榜
- 机器学习算法计算热度分数

**第4层 - 服务接口层**：
- GraphQL提供灵活查询接口
- Redis缓存热门查询结果
- CDN加速静态资源

**技术选型理由**：
- ClickHouse：时序数据查询性能好
- Kafka：高吞吐量消息处理
- Redis：毫秒级缓存响应

**记忆要点**：采集→存储→计算→服务

**总结**：这种系统就是典型的大数据架构，用Kafka收集数据，ClickHouse存储分析，Redis做缓存加速。

## 64. 正常派生和硬化派生的区别与联系

HD钱包派生有**2种不同方式**：

**正常派生（Non-hardened）**：
- 路径标记：m/0/1/2（数字小于2^31）
- 输入：父公钥 + 链码 + 索引
- 特点：可以从父公钥推导出子公钥
- 风险：子私钥泄露可能推导出父私钥

**硬化派生（Hardened）**：
- 路径标记：m/0'/1'/2'（数字大于等于2^31，带'符号）
- 输入：父私钥 + 链码 + 索引
- 特点：必须有父私钥才能派生
- 安全：子私钥泄露不会影响父私钥

**安全性对比**：
- 正常派生：便于公钥推导，但安全性较低
- 硬化派生：安全性高，但需要私钥参与

**实际应用**：
- BIP44路径：m/44'/coin_type'/account'/change/address_index
- 前3层用硬化派生保证安全
- 后2层用正常派生便于批量生成

**记忆要点**：带'号的是硬化派生，更安全但需要私钥

**总结**：硬化派生更安全但需要私钥，正常派生方便但有安全风险，实际使用中两种结合。

## 65 以太坊RLP序列化时ChainID的处理

- RLP（Recursive Length Prefix）：RLP是以太坊中用于序列化数据结构的编码方式，主要用于编码交易、区块等数据结构。

- ChainID的作用：ChainID用于区分不同的以太坊网络（如主网、测试网等），防止重放攻击（即在一个网络上的交易被复制到另一个网络）。

- ChainID在RLP中的处理：

    - 在以太坊交易中，ChainID是EIP-155引入的，用于签名交易。

    -
  在RLP编码中，ChainID会被包含在交易的签名数据中，具体格式为：[nonce, gasPrice, gasLimit, to, value, data, chainId, 0, 0]。

    - 签名时，ChainID会被编码到签名的v值中，公式为：v = recovery_id + 35 + chainId * 2。

- 总结：ChainID通过RLP编码和签名机制，确保交易只能在特定网络中生效。

## 66 Protobuf序列化之后的二进制结构

- Protobuf（Protocol Buffers）：是一种高效的二进制序列化格式，由Google开发，用于结构化数据的序列化和反序列化。

- 二进制结构：

    - Protobuf使用Tag-Length-Value（TLV）格式编码数据。

    - Tag：由字段编号和数据类型组成，占用1个或多个字节。

    - 字段编号：用于标识字段。

    - 数据类型：标识字段的类型（如Varint、64-bit、Length-delimited等）。

    - Length：对于长度可变的数据类型（如字符串、字节数组），会有一个长度字段。

    - Value：字段的实际值。

- 示例：

    - 对于字段int32 id = 1;，如果id = 42，编码结果为：08 2A。

        - 08：Tag（字段编号1，数据类型Varint）。

        - 2A：Value（42的Varint编码）。

- 总结：Protobuf的二进制结构紧凑高效，适合网络传输和存储。

## 67.Shamir共享秘密的本质和使用流程

Shamir共享秘密的本质和使用流程

- Shamir共享秘密：由Adi Shamir提出，是一种秘密共享方案，将秘密分成多个部分（称为“份额”），只有收集到足够数量的份额才能恢复秘密。

- 本质：

    - 基于多项式插值，将秘密编码为一个多项式的常数项。

    - 生成多个点（份额），只有收集到足够数量的点才能重建多项式并恢复秘密。

使用流程：

- 初始化：

    - 选择一个素数p作为有限域。

    - 选择一个秘密s（常数项）。

    - 生成一个k-1次多项式：f(x) = s + a₁x + a₂x² + ... + aₖ₋₁x^(k-1)。

- 生成份额：

    - 为每个参与者生成一个点(x, f(x))。

- 恢复秘密：

    - 收集至少k个点，使用拉格朗日插值法重建多项式，恢复秘密s。

- 总结：Shamir共享秘密是一种安全的分布式存储方案，适用于密钥管理和多方协作场景。

## 68.secp256k1和r1的区别，以及比特币和以太坊为何选择secp256k1

- secp256k1：

    - 椭圆曲线方程：y² = x³ + 7。

    - 特点：高效、计算速度快，适合区块链场景。

    - 使用范围：比特币、以太坊等区块链系统。

- secp256r1（也称为NIST P-256）：

    - 椭圆曲线方程：y² = x³ - 3x + b。

    - 特点：参数由NIST标准化，安全性较高，但计算效率略低于secp256k1。

    - 使用范围：TLS、SSL等传统加密场景。

- 区别：

    - 曲线参数不同：secp256k1的曲线参数简单，secp256r1的参数由NIST定义。

    - 性能：secp256k1在签名和验证速度上更快。

    - 安全性：两者均被认为安全，但secp256k1的简单性使其更受区块链青睐。

- 比特币和以太坊选择secp256k1的原因：

    - 效率：secp256k1的计算速度更快，适合区块链的高频交易场景。

    - 透明性：secp256k1的曲线参数简单，避免了NIST可能存在的后门争议。

    - 社区共识：比特币率先采用secp256k1，以太坊为兼容性和一致性也选择该曲线。


## 69. 回滚区块的时候，是怎么确认回滚哪个块

区块回滚确认有**3个判断标准**：

**标准1 - 分叉检测**：
- 比较本地链和网络主链的区块哈希
- 找到分叉点（最后一个相同的区块）
- 从分叉点之后开始回滚

**标准2 - 链长度比较**：
- 选择最长的有效链作为主链
- 回滚到较短链上的区块
- 遵循最长链原则

**标准3 - 工作量证明**：
- 比较累积工作量（难度总和）
- 选择累积工作量最大的链
- 回滚工作量较小的分支

**回滚流程4步**：
1. 检测到分叉或重组
2. 确定分叉点位置
3. 回滚分叉点之后的区块
4. 应用新链上的区块

**记忆要点**：分叉点、最长链、累积工作量

**总结**：回滚就是找到分叉点，然后选择最长或工作量最大的链，把短链上的区块撤销掉。

## 70. mpc 签名后的交易 signature 是明文，不需要加密嘛？不会被黑客盗走

MPC签名后的signature确实是明文，但这**没有安全问题**，原因有3点：

**原因1 - 签名本质**：
- 签名是用来证明交易合法性的
- 签名本身不包含私钥信息
- 即使被看到也无法推导出私钥

**原因2 - 公开性需求**：
- 区块链网络需要验证签名
- 所有节点都要能读取签名
- 签名必须是公开可见的

**原因3 - 数学安全性**：
- 基于椭圆曲线离散对数难题
- 从签名推导私钥在计算上不可行
- 即使量子计算也需要很长时间

**真正的安全风险**：
- 私钥泄露：这才是真正的风险
- 签名过程被攻击：MPC协议本身的安全
- 重放攻击：需要nonce等机制防护

**记忆要点**：签名公开无害，私钥保密才重要

**总结**：签名就是要公开的，不需要加密，真正要保护的是私钥和签名过程的安全。

## 71. 如果 solana 那边区块节点挂了，出现了区块堆积，他们那边恢复以后，我们从最新区块接着扫，然后断掉的区块用另一个定时任务去扫，但是流水表(交易日报)怎么接上去

Solana节点恢复后的数据接续有**4个处理策略**：

**策略1 - 时间戳标记**：
- 每笔交易记录实际发生时间
- 按交易时间而非扫描时间归类
- 确保日报数据的时间准确性

**策略2 - 补扫标识**：
- 给补扫的交易打上特殊标记
- 区分实时扫描和补扫数据
- 避免重复统计

**策略3 - 分批处理**：
- 补扫完成后批量更新日报
- 重新计算受影响时间段的统计数据
- 保证数据一致性

**策略4 - 版本控制**：
- 日报数据支持版本管理
- 补扫后生成新版本日报
- 保留历史版本便于对比

**技术实现**：
- 使用事务保证数据一致性
- 设置补扫完成标志位
- 定时任务检查并更新日报

**记忆要点**：时间戳、补扫标识、分批处理、版本控制

**总结**：关键是按交易实际时间归类，补扫完成后重新计算对应时间段的日报数据。

## 72. 怎么解决重放攻击，有些链是没有链 ID，比如 xlm 链没有链ID

没有ChainID的链防重放攻击有**4种方案**：

**方案1 - Sequence Number**：
- 每个账户维护递增的序列号
- 如Stellar使用sequence number
- 确保交易顺序和唯一性

**方案2 - 时间戳窗口**：
- 交易包含有效时间范围
- 超过时间窗口的交易无效
- 防止旧交易被重放

**方案3 - 网络特定参数**：
- 使用网络特有的参数（如genesis hash）
- 在交易中包含网络标识信息
- 不同网络无法重放

**方案4 - Memo字段**：
- 在交易memo中包含网络标识
- 或使用随机nonce值
- 增加交易的唯一性

**Stellar具体实现**：
- 使用sequence number防重放
- 支持时间边界（time bounds）
- 每个网络有不同的passphrase

**记忆要点**：序列号、时间窗口、网络参数、Memo标识

**总结**：没有ChainID就用sequence number和时间窗口，关键是让每笔交易在不同网络上都不一样。

## 73. 你们项目的归集是怎么做的? 那么大批量的归集（20-30万笔）, 是一笔一笔的签名吗?

大批量归集有**3种优化策略**：

**策略1 - 批量签名**：
- 使用MPC批量签名技术
- 一次签名过程处理多笔交易
- 大幅提升签名效率

**策略2 - 并行处理**：
- 多个签名机并行工作
- 按地址分片并行签名
- 20-30万笔可以分成多批同时处理

**策略3 - 智能合约批处理**：
- 部署批量转账合约
- 一笔交易处理多个转账
- 节省gas费用和时间

**具体实现**：
- 按余额大小排序，优先归集大额
- 设置最小归集金额阈值
- 使用队列管理归集任务

**性能数据**：
- 单个签名机：约100-200笔/分钟
- 10个并行签名机：1000-2000笔/分钟
- 30万笔大约需要2-5小时

**记忆要点**：批量签名、并行处理、智能合约

**总结**：大批量归集肯定不是一笔一笔签名，要用批量签名和并行处理，还可以用智能合约批处理。

## 74. 你们项目的充值提现有没有做过什么安全措施

钱包安全措施有**5个层面的防护**：

**防护1 - 地址验证**：
- 充值地址白名单机制
- 提现地址格式校验
- 防止假充值和错误转账

**防护2 - 金额限制**：
- 单笔提现限额
- 日累计提现限额
- 大额提现需要人工审核

**防护3 - 多重签名**：
- 热钱包使用多签机制
- 重要操作需要多人确认
- 防止单点故障

**防护4 - 风控系统**：
- 异常交易检测
- 用户行为分析
- 可疑操作自动冻结

**防护5 - 冷热分离**：
- 大部分资金存储在冷钱包
- 热钱包只保留必要资金
- 定期转冷降低风险

**技术措施**：
- 交易确认位设置
- 实时监控和告警
- 定期安全审计

**记忆要点**：地址验证、金额限制、多重签名、风控系统、冷热分离

**总结**：安全措施就是多层防护，从地址验证到风控系统，每个环节都要有安全保障。

## 75. 你们签名机的热钱包和冷钱包是怎么进行数据隔离和权限隔离的?

热冷钱包隔离有**4个层面的设计**：

**层面1 - 物理隔离**：
- 冷钱包完全离线，无网络连接
- 热钱包在线处理日常交易
- 物理上分离存储设备

**层面2 - 网络隔离**：
- 冷钱包使用独立网络环境
- 热钱包通过防火墙限制访问
- 不同网络段，无法直接通信

**层面3 - 权限隔离**：
- 冷钱包需要多人授权操作
- 热钱包有自动化权限
- 不同角色有不同访问权限

**层面4 - 数据隔离**：
- 冷钱包私钥加密存储
- 热钱包只存储必要的操作密钥
- 敏感数据分级管理

**操作流程**：
- 日常交易：热钱包自动处理
- 大额转账：需要冷钱包签名
- 资金补充：冷转热人工操作

**记忆要点**：物理隔离、网络隔离、权限隔离、数据隔离

**总结**：冷热钱包隔离就是物理分离、网络分离、权限分离，确保冷钱包绝对安全。

## 76. 合约内部交易是怎么解析的

合约内部交易解析有**4个关键步骤**：

**步骤1 - 获取交易Receipt**：
- 调用eth_getTransactionReceipt接口
- 获取交易执行结果和日志
- 包含所有事件日志信息

**步骤2 - 解析Event日志**：
- 根据ABI解码事件日志
- 提取Transfer、Approval等关键事件
- 获取实际的资金流向

**步骤3 - 追踪内部调用**：
- 使用debug_traceTransaction接口
- 获取合约内部调用链
- 分析每一步的状态变化

**步骤4 - 构建交易图谱**：
- 将所有内部交易组织成树状结构
- 标识每个调用的成功/失败状态
- 计算最终的余额变化

**技术要点**：
- Event日志是最可靠的数据源
- 内部交易可能失败但外部交易成功
- 需要处理合约自毁等特殊情况

**记忆要点**：Receipt→Event→内部调用→交易图谱

**总结**：合约内部交易主要靠解析Event日志，配合debug接口追踪内部调用链，构建完整的交易图谱。

## 77. 用第三节点和自建节点的区别与联系

第三方节点vs自建节点有**4个维度的对比**：

**维度1 - 成本对比**：
- 第三方节点：按使用量付费，初期成本低
- 自建节点：硬件+运维成本高，长期更便宜

**维度2 - 可靠性对比**：
- 第三方节点：可能有限流和宕机风险
- 自建节点：完全可控，但需要自己保证稳定性

**维度3 - 性能对比**：
- 第三方节点：共享带宽，可能有延迟
- 自建节点：专用资源，性能更好

**维度4 - 安全性对比**：
- 第三方节点：数据经过第三方，有泄露风险
- 自建节点：数据完全私有，更安全

**选择建议**：
- 初期/小规模：使用第三方节点
- 大规模/高要求：自建节点
- 混合方案：主用自建，备用第三方

**记忆要点**：成本、可靠性、性能、安全性

**总结**：第三方节点适合快速启动，自建节点适合大规模应用，实际中经常混合使用。

## 78. 如何获取 Solana 智能合约的 IDL

获取Solana合约IDL有**4种方法**：

**方法1 - Anchor程序**：
- 使用anchor idl fetch命令
- 从链上获取IDL数据
- 适用于Anchor框架开发的程序

**方法2 - 程序账户查询**：
- 查询程序的IDL账户
- IDL通常存储在特定的PDA地址
- 需要知道IDL账户的推导规则

**方法3 - 官方文档/GitHub**：
- 查看项目的官方文档
- 从GitHub仓库获取IDL文件
- 适用于开源项目

**方法4 - 第三方工具**：
- 使用Solscan等区块链浏览器
- 一些工具提供IDL解析功能
- 或使用专门的IDL提取工具

**技术实现**：
```bash
# Anchor命令示例
anchor idl fetch <PROGRAM_ID>
```

**注意事项**：
- 不是所有程序都有IDL
- 非Anchor程序可能没有标准IDL格式

**记忆要点**：Anchor命令、程序账户、官方文档、第三方工具

**总结**：获取Solana IDL最直接的是用anchor idl fetch命令，没有的话就去官方文档找。

## 79. uniswap上发一笔交易，过程发生了什么越详细越好

Uniswap交易有**7个详细步骤**：

**步骤1 - 用户发起交易**：
- 用户在前端选择代币对和数量
- 前端调用Uniswap路由合约计算最优路径
- 生成交易参数（滑点、截止时间等）

**步骤2 - 授权代币**：
- 如果是首次交易该代币，需要先approve
- 调用ERC20的approve函数授权路由合约
- 设置授权额度

**步骤3 - 发送交易到内存池**：
- 钱包签名交易并广播到网络
- 交易进入内存池等待矿工打包
- 可能面临MEV机器人的夹子攻击

**步骤4 - 矿工打包执行**：
- 矿工选择交易打包到区块
- 路由合约执行swap逻辑
- 调用流动性池的swap函数

**步骤5 - AMM计算和执行**：
- 根据x*y=k公式计算兑换比例
- 更新池子的代币余额
- 收取0.3%的手续费给LP

**步骤6 - 代币转移**：
- 从用户地址转出输入代币
- 向用户地址转入输出代币
- 触发Transfer事件

**步骤7 - 交易确认**：
- 区块被确认后交易生效
- 前端更新用户余额显示
- 价格预言机更新价格信息

**总结**：Uniswap交易就是授权→路由→AMM计算→代币转移的过程，核心是x*y=k的自动做市商机制。

## 80. 请详细说一下 Solana 交易的解析问题

Solana交易解析有**5个关键难点**：

**难点1 - 指令数据解析**：
- Solana指令数据是二进制格式
- 需要根据程序ID和指令类型解析
- 不同程序有不同的数据结构

**难点2 - 账户变化追踪**：
- 交易前后账户余额变化
- 需要对比pre和post状态
- 包括SOL和SPL代币的变化

**难点3 - 程序日志解析**：
- 程序执行过程中的日志输出
- 包含重要的业务逻辑信息
- 需要根据程序类型解析日志格式

**难点4 - 内部指令处理**：
- 一个交易可能包含多个指令
- 指令之间可能有依赖关系
- 需要按顺序解析每个指令

**难点5 - 错误处理**：
- 交易失败时的错误信息解析
- 部分指令成功部分失败的情况
- 需要准确识别失败原因

**解析策略**：
- 使用官方SDK解析标准指令
- 自定义解析器处理特殊程序
- 结合IDL文件解析Anchor程序

**记忆要点**：指令解析、账户变化、程序日志、内部指令、错误处理

**总结**：Solana交易解析主要难在指令数据是二进制的，需要根据不同程序类型用不同方法解析。

## 81. 交易所储备金证明系统怎么做的

储备金证明系统有**4个核心组件**：

**组件1 - 资产快照**：
- 定期对所有钱包地址进行余额快照
- 包括热钱包、冷钱包、多签钱包
- 生成默克尔树根哈希

**组件2 - 负债统计**：
- 统计所有用户的资产总额
- 按币种分类计算负债
- 生成负债证明文件

**组件3 - 默克尔树证明**：
- 用户可以验证自己的资产被包含在负债统计中
- 提供默克尔路径证明
- 保护用户隐私不泄露具体余额

**组件4 - 第三方审计**：
- 邀请知名审计公司验证
- 公开钱包地址供社区监督
- 定期发布审计报告

**技术实现**：
- 使用零知识证明保护隐私
- 区块链上发布证明哈希
- 提供开源验证工具

**证明公式**：资产 ≥ 负债

**记忆要点**：资产快照、负债统计、默克尔证明、第三方审计

**总结**：储备金证明就是证明交易所的资产大于等于用户负债，用默克尔树保护隐私，第三方审计增加可信度。

## 82. 大规模的批量提现

大规模批量提现需要**5个优化策略**：

**策略1 - 分批处理**：
- 将大批量拆分成小批次
- 每批次1000-5000笔交易
- 避免系统过载和网络拥堵

**策略2 - 并行签名**：
- 多个签名机并行工作
- 按地址或金额分片处理
- 提高整体处理速度

**策略3 - 智能合约批处理**：
- 部署批量转账合约
- 一笔交易处理多个转账
- 大幅节省gas费用

**策略4 - 动态手续费**：
- 根据网络拥堵情况调整gas价格
- 优先级队列管理
- 重要提现优先处理

**策略5 - 失败重试机制**：
- 自动检测失败交易
- 智能重试策略
- 人工干预处理异常

**性能指标**：
- 处理速度：10000+笔/小时
- 成功率：>99.5%
- 平均确认时间：<10分钟

**记忆要点**：分批处理、并行签名、批处理合约、动态手续费、失败重试

**总结**：大规模提现关键是分批+并行+批处理合约，既要保证速度又要控制成本和风险。

## 83.假充值怎么解决

假充值例子：
- 伪造代币合约充值：部署假的代币合约，模仿ETH、USDT代币的名称和符号，但合约地址不一样。通过调用合约方法，直接修改目标钱包的余额显示。
    - 解决方式：
        - 钱包系统内有白名单地址，只有配置了白名单的合约，才能往交易所内充值；
        - 代币充值，钱包也是有个token表来配置，只有配置了的代币才能往里面冲。
- 虚假转账记录：通过伪造交易数据，让钱包显示收到一笔转账，但这笔交易并未发生在链上。
    - 技术原理：
        - 利用钱包软件的漏洞，直接修改本地显示数据（较少见，需用户使用被篡改的钱包）
        - 或通过虚假节点返回伪造的区块链数据（针对未验证的 RPC 端点）
    - 解决方式：使用一个RPC节点，来执行一笔转账交易，但不上链，看看能否成功，用来表明该账户是不是有充值这么多金额

一般解决方式：
- 通过区块链浏览器检查交易记录
- 验证合约地址
- 测试转账
- 请求风控校验

## 84.交易所资产证明，证明自己有钱

- https://github.com/the-web3/chaineye-binance-por 币安的资产证明的整个项目概述

## 85.私钥怎么和助记词对应，路径推导的协议怎么推导出来的

如果是使用12个助记词的话，首先生成128位随机熵，然后进行SHA-256哈希计算得到哈希值，取出哈希值的前四位，拼接到随机熵后面。之后取每十一位来转换成10进制索引，从2048个单词里得到相应的助记词。
然后使用PBKDF2基于密码的密钥派生函数，以助记词为入参，对助记词进行2028次哈希计算（HMAC-SHA512），得到512位（64字节）的Seed。

种子生成后，通过BIP32标准推导主私钥和主链码：

对种子使用HMAC-SHA512计算，得到主私钥（前32字节）和主链码（后32字节）

## 86. 怎么算solana多少cu，一个账户占多少字节

Solana的CU计算和账户大小有**3个关键概念**：

**CU（Compute Units）计算**：
- 每个指令消耗不同的CU
- 简单转账：约21000 CU
- 复杂DeFi操作：可能需要200000+ CU
- 通过simulateTransaction预估

**账户大小计算**：
- 基础账户：0字节（只有地址）
- SOL账户：实际不占用存储空间
- SPL Token账户：165字节
- 自定义程序账户：根据数据结构计算

**租金计算公式**：
- 租金 = 账户大小 × 每字节租金率
- SPL Token账户租金：约0.00204 SOL
- 大账户需要更多租金

**实际测量方法**：
- 使用getAccountInfo查看账户大小
- simulateTransaction查看CU消耗
- 监控交易实际消耗

**常见数据**：
- 系统转账：21000 CU
- SPL转账：约30000 CU
- Uniswap交易：100000+ CU

**记忆数字**：21000 CU转账，165字节Token账户

**总结**：CU通过模拟交易预估，账户大小看数据结构，SPL Token账户固定165字节需要租金。

## 87.Solana签名，怎么签 feepayer，两个签名怎么拼

签名规则：
- feePayer必须是第一个签名者
- 其他签名者按accountKeys中需要签名的顺序排列
- 签名者对消息部分（Message）的哈希进行签名
  问题场景：
- 目标：feePayer使用私钥A签名，指令的发起者使用私钥B签名
- 挑战：
    - 默认情况下，Transaction.sign会用单一私钥签名所有需要签名的账户
    - 需要分离签名过程，确保每个私钥只签名其对应的账户
      手动拼接步骤：
- 构造交易信息：
    - 使用@solana/web3.js创建交易并提取消息部分
    - 设置feePayer和其他签名账户
- 序列化消息：获取交易的Message部分并序列化，用于签名
- 分别签名：
    - 使用每个私钥对消息进行独立签名
    - 确保签名顺序与accountKeys中需要签名的账户顺序一致
- 拼接交易：将签名数组和消息部分手动合成完整的交易字节数组
- 验证和发送：将拼接后的交易提交到网络

```
import { Connection, Keypair, Transaction, SystemProgram, PublicKey } from "@solana/web3.js";
import { sign } from "@solana/web3.js/lib/ed25519"; // 手动签名工具

const connection = new Connection("https://api.devnet.solana.com", "confirmed");

// 生成两个密钥对
const feePayer = Keypair.generate(); // 支付费用的账户
const sender = Keypair.generate();   // 转账发起者
const recipient = Keypair.generate(); // 接收者

async function createAndSignTransaction() {
  // 构造交易
  const tx = new Transaction();
  const { blockhash } = await connection.getLatestBlockhash();
  tx.recentBlockhash = blockhash;
  tx.feePayer = feePayer.publicKey;

  // 添加转账指令
  tx.add(
    SystemProgram.transfer({
      fromPubkey: sender.publicKey,
      toPubkey: recipient.publicKey,
      lamports: 1000000, // 1 SOL
    })
  );

  // 编译消息（不签名）
  const message = tx.compileMessage();
  const serializedMessage = message.serialize();

  // 分别签名
  const feePayerSignature = sign(serializedMessage, feePayer.secretKey); // feePayer 签名
  const senderSignature = sign(serializedMessage, sender.secretKey);     // sender 签名

  // 手动拼接交易
  const numSignatures = 2; // feePayer + sender
  const signatures = [
    feePayerSignature, // 第一个签名是 feePayer
    senderSignature,   // 第二个签名是 sender
  ];

  // 创建完整的交易字节数组
  const txBytes = Buffer.concat([
    Buffer.from([numSignatures]), // 签名数量
    ...signatures.map(sig => Buffer.from(sig)), // 签名数组
    Buffer.from(serializedMessage), // 消息部分
  ]);

  // 转换为 base58 编码（可选，用于 RPC）
  const base58 = require("bs58");
  const encodedTx = base58.encode(txBytes);

  // 模拟交易（验证）
  const simulation = await connection.simulateTransaction(
    Transaction.from(txBytes),
    { commitment: "confirmed" }
  );
  console.log("Simulation Result:", simulation.value);

  // 发送交易（需确保账户有足够 SOL）
  // const signature = await connection.sendRawTransaction(txBytes);
  // console.log("Transaction Signature:", signature);
}

createAndSignTransaction().catch(console.error);
```
构造交易消息：创建交易并设置 feePayer 和指令。
序列化消息：提取交易的 Message 并序列化为字节数组。
分别签名：使用每个私钥对消息签名。
拼接交易：将签名和消息组合成完整交易。
验证和发送：通过 RPC 模拟或提交交易。

Golang实现方式：
```
package main

import (
        "context"
        "fmt"
        "log"

        "github.com/gagliardetto/solana-go"
        "github.com/gagliardetto/solana-go/programs/system"
        "github.com/gagliardetto/solana-go/rpc"
        "github.com/gagliardetto/solana-go/text"
)

func main() {
        // 初始化 RPC 客户端
        client := rpc.New("https://api.devnet.solana.com")

        // 生成密钥对
        feePayer, err := solana.NewRandomPrivateKey()
        if err != nil {
                log.Fatalf("Failed to generate feePayer key: %v", err)
        }
        sender, err := solana.NewRandomPrivateKey()
        if err != nil {
                log.Fatalf("Failed to generate sender key: %v", err)
        }
        recipient := solana.NewWallet().PublicKey()

        fmt.Printf("FeePayer: %s\n", feePayer.PublicKey())
        fmt.Printf("Sender: %s\n", sender.PublicKey())
        fmt.Printf("Recipient: %s\n", recipient)

        // 获取最近区块哈希
        ctx := context.Background()
        recent, err := client.GetLatestBlockhash(ctx, rpc.CommitmentFinalized)
        if err != nil {
                log.Fatalf("Failed to get recent blockhash: %v", err)
        }

        // 构造交易
        tx := solana.NewTransactionBuilder().
                SetFeePayer(feePayer.PublicKey()).
                SetRecentBlockHash(recent.Value.Blockhash).
                AddInstruction(
                        system.NewTransferInstruction(
                                1000000, // 1 SOL
                                sender.PublicKey(),
                                recipient,
                        ).Build(),
                )

        // 编译消息（不签名）
        message, err := tx.Build()
        if err != nil {
                log.Fatalf("Failed to build message: %v", err)
        }

        // 序列化消息
        serializedMessage, err := message.MarshalBinary()
        if err != nil {
                log.Fatalf("Failed to serialize message: %v", err)
        }

        // 分别签名
        feePayerSig, err := feePayer.Sign(serializedMessage)
        if err != nil {
                log.Fatalf("Failed to sign with feePayer: %v", err)
        }
        senderSig, err := sender.Sign(serializedMessage)
        if err != nil {
                log.Fatalf("Failed to sign with sender: %v", err)
        }

        // 拼接交易
        signatures := []solana.Signature{feePayerSig, senderSig}
        completeTx := &solana.Transaction{
                Signatures: signatures,
                Message:    *message,
        }

        // 序列化为字节数组（可选，用于手动检查）
        txBytes, err := completeTx.MarshalBinary()
        if err != nil {
                log.Fatalf("Failed to marshal transaction: %v", err)
        }

        // 模拟交易
        simResult, err := client.SimulateTransactionWithOpts(
                ctx,
                completeTx,
                &rpc.SimulateTransactionOpts{
                        SigVerify:              false, // 不验证签名（测试用）
                        Commitment:             rpc.CommitmentFinalized,
                        ReplaceRecentBlockhash: false,
                },
        )
        if err != nil {
                log.Fatalf("Failed to simulate transaction: %v", err)
        }

        // 输出结果
        if simResult.Value.Err != nil {
                fmt.Printf("Simulation failed: %v\n", simResult.Value.Err)
        } else {
                fmt.Printf("Simulation succeeded:\n")
                fmt.Printf("Units Consumed: %d\n", simResult.Value.UnitsConsumed)
                for _, log := range simResult.Value.Logs {
                        fmt.Println(log)
                }
        }

        // 发送交易（需确保账户有资金）
        // sig, err := client.SendTransaction(ctx, completeTx)
        // if err != nil {
        //         log.Fatalf("Failed to send transaction: %v", err)
        // }
        // fmt.Printf("Transaction Signature: %s\n", sig)
}
```

## 88. 哈希环是不是一致性hash算法？

哈希环**就是**一致性哈希算法的核心实现，有**3个关键特点**：

**特点1 - 环形结构**：
- 将哈希值空间组织成环形
- 通常是0到2^32-1的环
- 节点和数据都映射到环上

**特点2 - 顺时针查找**：
- 数据存储在顺时针方向第一个节点
- 节点下线时数据迁移到下一个节点
- 最小化数据迁移量

**特点3 - 虚拟节点**：
- 每个物理节点映射多个虚拟节点
- 解决数据分布不均匀问题
- 提高负载均衡效果

**应用场景**：
- 分布式缓存（如Redis Cluster）
- 分布式存储系统
- 负载均衡器

**优势**：
- 节点增减时只影响相邻节点
- 数据迁移量最小
- 扩展性好

**记忆要点**：环形结构、顺时针查找、虚拟节点

**总结**：哈希环就是一致性哈希的具体实现方式，通过环形结构实现最小化数据迁移的分布式哈希。


## 89. 布隆过滤器，怎么避免误判？

布隆过滤器避免误判有**4种优化策略**：

**策略1 - 增加位数组大小**：
- 更大的位数组降低冲突概率
- 位数组大小与元素数量比例要合适
- 通常建议10-20倍的空间

**策略2 - 优化哈希函数数量**：
- 哈希函数太少：误判率高
- 哈希函数太多：计算开销大
- 最优数量：k = (m/n) * ln(2)

**策略3 - 分层布隆过滤器**：
- 使用多个布隆过滤器
- 第一层过滤大部分数据
- 第二层进一步精确过滤

**策略4 - 计数布隆过滤器**：
- 用计数器替代单个位
- 支持删除操作
- 可以动态调整

**误判率公式**：
- P = (1 - e^(-kn/m))^k
- k：哈希函数数量，n：元素数量，m：位数组大小

**实际应用**：
- 设置合理的误判率阈值（如1%）
- 重要数据用精确查询验证
- 定期重建过滤器

**记忆要点**：增大位数组、优化哈希数量、分层过滤、计数过滤

**总结**：布隆过滤器误判无法完全避免，只能通过增大空间、优化参数、分层设计来降低误判率。


## 90. 工作中有没有遇到资金损失，或者系统故障，怎么解决的

资金损失和系统故障处理有**4个应对策略**：

**策略1 - 预防措施**：
- 多重签名和权限控制
- 代码审计和安全测试
- 实时监控和告警系统
- 定期备份和灾难恢复演练

**策略2 - 快速响应**：
- 立即暂停相关服务
- 启动应急响应小组
- 评估损失范围和影响
- 通知用户和监管机构

**策略3 - 问题定位**：
- 分析日志和交易记录
- 复现问题场景
- 确定根本原因
- 制定修复方案

**策略4 - 恢复处理**：
- 修复系统漏洞
- 恢复正常服务
- 用户资产补偿
- 总结经验教训

**常见故障类型**：
- 私钥泄露：立即转移资金到安全地址
- 合约漏洞：暂停合约，升级修复
- 系统宕机：切换备用系统

**记忆要点**：预防、响应、定位、恢复

**总结**：遇到资金损失要立即止损，快速定位问题，修复后做好用户补偿和经验总结。


## 91. 提现成功率多少

提现成功率的**3个评估维度**：

**维度1 - 行业标准**：
- 优秀钱包：>99.5%成功率
- 一般钱包：95-99%成功率
- 目标：99.8%以上

**维度2 - 影响因素**：
- 网络拥堵：可能降低到95%
- 系统维护：暂时降低成功率
- 用户操作错误：地址错误、余额不足

**维度3 - 优化措施**：
- 智能手续费调整
- 失败交易自动重试
- 用户操作前置校验

**统计方法**：
- 成功率 = 成功交易数 / 总交易数 × 100%
- 按时间段统计（日/周/月）
- 分币种统计成功率

**记忆数字**：99.5%是优秀标准

**总结**：好的钱包提现成功率应该在99.5%以上，通过智能手续费和重试机制来保证。


## 92. 每条链，每天多少笔

各链日交易量的**3个层级分类**：

**第1层级 - 主流链（百万级）**：
- 以太坊：100-150万笔/天
- 比特币：30-50万笔/天
- BSC：300-500万笔/天

**第2层级 - 热门链（十万级）**：
- Polygon：50-100万笔/天
- Solana：20-50万笔/天
- Arbitrum：10-30万笔/天

**第3层级 - 小众链（万级）**：
- Cosmos：1-5万笔/天
- Avalanche：5-15万笔/天
- 其他新链：几千到几万笔

**影响因素**：
- 网络活跃度和生态发展
- Gas费用高低
- DeFi和NFT活动

**钱包处理能力**：
- 大型交易所：每天处理几十万笔
- 中型钱包：每天几万笔
- 小型钱包：每天几千笔

**记忆数字**：ETH 100万，BTC 50万，BSC 500万

**总结**：主流链每天百万笔交易，钱包要根据支持的链来规划处理能力。


## 93. 钱包对账怎么做的

钱包对账有**4个核心环节**：

**环节1 - 数据收集**：
- 收集链上真实余额数据
- 收集系统内部账本数据
- 收集用户充值提现记录

**环节2 - 余额对比**：
- 按币种对比链上余额vs系统余额
- 检查是否存在差异
- 标记异常账户

**环节3 - 交易核对**：
- 核对每笔充值是否正确入账
- 核对每笔提现是否正确扣账
- 检查是否有遗漏或重复

**环节4 - 差异处理**：
- 分析差异产生原因
- 调整系统账本数据
- 记录对账结果

**对账频率**：
- 实时对账：重要交易立即核对
- 日对账：每日定时全面对账
- 月对账：月度完整性检查

**自动化工具**：
- 定时任务自动执行
- 异常情况自动告警
- 对账报告自动生成

**记忆要点**：收集、对比、核对、处理

**总结**：钱包对账就是定期比较链上真实余额和系统账本，发现差异及时调整。


## 93.BTC粉尘资金多少
- P2PKH 546聪
- P2SH 828聪
- P2WPKH 270聪
- P2WSH 417聪
- P2TR 276聪

## 94. Solana匹配交易的时候，lookup account什么时候用

Lookup Account在**3种场景**下使用：

**场景1 - 账户数量超限**：
- Solana交易最多包含64个账户
- 复杂DeFi操作可能需要更多账户
- 使用Lookup Table压缩账户引用

**场景2 - 减少交易大小**：
- 账户地址占用32字节
- Lookup Table中用1字节索引代替
- 大幅减少交易数据量

**场景3 - 批量操作优化**：
- 批量转账涉及大量账户
- 预先创建Lookup Table
- 提高交易处理效率

**使用流程4步**：
1. 创建Address Lookup Table
2. 添加常用账户地址到表中
3. 交易中引用Lookup Table
4. 用索引代替完整地址

**优势**：
- 突破64账户限制
- 减少交易费用
- 提高网络效率

**记忆要点**：超64账户、减少大小、批量优化

**总结**：Lookup Account主要用于突破64个账户限制和减少交易大小，特别适合复杂DeFi操作。


## 95.比特币usdt怎么实现的？
比特币原生不支持发行代币，USDT 是通过Omni Layer 协议在比特币之上“搭建了一层”，来实现“代币发行和转账”的。
- blockchain-wallet/Omni/README.md at master · the-web3/blockchain-wallet
- https://github.com/OmniLayer/spec

## 96.BTC你们怎么使用账本的，提现时使用哪种账本，怎么做
- 提现时，通过最小化找零算法，来找一个金额相似的账本用来提现

## 97.不同layer2有些用的debug_traceTransaction，有些不支持，你们用的什么或者你知道接口名字吗

- 支持debug_traceTransaction的链：Optimism、Arbitrum、Polygon PoS
- 不支持的链：
    - zkSync：使用zks_getTransactionDetails接口

## 98. solana使用getblock接口，里面encoding参数json、jsonParsed有什么区别？

Solana getBlock接口的encoding参数有**3种主要区别**：

**json格式**：
- 返回原始的二进制数据（base64编码）
- 交易数据需要手动解析
- 数据量较小，传输效率高
- 适合需要自定义解析的场景

**jsonParsed格式**：
- RPC节点预先解析了交易数据
- 返回人类可读的结构化数据
- 包含指令类型、账户信息等
- 数据量较大，但使用方便

**base58格式**：
- 交易以base58字符串形式返回
- 主要用于交易哈希和签名
- 兼容性好，但需要额外解析

**使用场景对比**：
- json：高性能应用，自定义解析逻辑
- jsonParsed：快速开发，展示用途
- base58：兼容性要求高的场景

**性能差异**：
- json：解析速度快，网络传输小
- jsonParsed：解析速度慢，网络传输大

**记忆要点**：json原始数据，jsonParsed解析数据，base58兼容格式

**总结**：json返回原始数据需要自己解析，jsonParsed返回解析好的数据直接用，看需求选择。


## 99. 钱包导入助记词，有些钱包就把我不同地址资产识别出来了，怎么做的

钱包自动识别资产有**4个技术步骤**：

**步骤1 - 地址派生**：
- 根据BIP44标准派生多个地址
- 通常扫描前100-1000个地址
- 包括不同币种的派生路径

**步骤2 - 批量查询余额**：
- 并行查询所有派生地址的余额
- 使用批量RPC接口提高效率
- 过滤掉余额为0的地址

**步骤3 - 代币合约扫描**：
- 查询每个地址的代币余额
- 扫描常见的代币合约
- 使用代币列表API获取元数据

**步骤4 - 交易历史分析**：
- 分析地址的交易历史
- 识别曾经使用过的地址
- 发现可能的隐藏资产

**优化策略**：
- 使用缓存减少重复查询
- 增量扫描新的代币合约
- 用户手动添加自定义代币

**技术实现**：
- 多线程并行查询
- 智能合约批量调用
- 本地数据库缓存结果

**记忆要点**：地址派生、批量查询、代币扫描、历史分析

**总结**：钱包就是把助记词派生出很多地址，然后批量查询余额和代币，把有资产的地址显示出来。

## 100. 一个私钥，在一个spl-token里面可以有几个 ata 账户

一个私钥对于一个SPL代币**只能有1个ATA账户**，原因有3点：

**原因1 - 确定性派生**：
- ATA地址通过算法确定性生成
- 基于用户地址 + 代币mint地址 + ATA程序ID
- 相同输入永远产生相同输出

**原因2 - 唯一性保证**：
- 防止用户为同一代币创建多个账户
- 简化钱包和DApp的集成逻辑
- 避免资金分散和管理复杂性

**原因3 - 程序设计**：
- ATA程序强制执行一对一关系
- 尝试创建重复ATA会失败
- 这是Solana生态的设计标准

**ATA地址计算公式**：
```
ATA = findProgramAddress([
  userAddress,
  TOKEN_PROGRAM_ID,
  mintAddress
], ASSOCIATED_TOKEN_PROGRAM_ID)
```

**特殊情况**：
- 可以创建非ATA的代币账户（多个）
- 但钱包通常只识别ATA账户
- 非ATA账户需要手动管理

**记忆要点**：1个私钥1个代币1个ATA，确定性派生保证唯一

**总结**：一个私钥对一个SPL代币只能有一个ATA账户，这是通过确定性地址派生算法保证的。

## 100. BTC getBlock接口

BTC getBlock接口有**3个关键参数**：

**参数1 - blockhash**：
- 区块哈希值（必需参数）
- 用于指定要获取的区块
- 可以通过getblockhash获取

**参数2 - verbosity**：
- 0：返回原始区块数据（十六进制）
- 1：返回JSON格式的区块信息
- 2：返回包含交易详情的完整信息

**参数3 - 返回数据结构**：
- 区块头信息：时间戳、难度、前一区块哈希
- 交易列表：所有交易的详细信息
- 确认数：当前确认数量

**使用示例**：
```bash
# 获取JSON格式区块信息
bitcoin-cli getblock "blockhash" 1

# 获取包含交易详情的完整信息
bitcoin-cli getblock "blockhash" 2
```

**常用场景**：
- 扫链解析交易
- 验证交易确认状态
- 分析区块链数据

**记忆要点**：blockhash必需，verbosity控制详细程度

**总结**：BTC getBlock接口通过区块哈希获取区块信息，verbosity参数控制返回数据的详细程度。

## 101. 如何验证一笔交易是合法的。在RPC节点里面是否可以验证？如果可以的话，RPC节点是如何验证这个交易是合法的（具体验证哪些字段）

RPC节点**可以验证**交易合法性，有**5个验证层面**：

**层面1 - 格式验证**：
- 交易结构是否符合协议规范
- 字段类型和长度是否正确
- 序列化格式是否有效

**层面2 - 签名验证**：
- 验证数字签名的有效性
- 检查签名者是否有权限
- 确认签名与交易内容匹配

**层面3 - 余额验证**：
- 检查发送者余额是否充足
- 验证UTXO是否存在且未花费（BTC）
- 确认账户状态正确（ETH）

**层面4 - 业务逻辑验证**：
- Gas费用是否合理
- Nonce是否正确递增
- 合约调用参数是否有效

**层面5 - 网络规则验证**：
- 交易费用是否满足最低要求
- 交易大小是否在限制范围内
- 是否违反网络特定规则

**验证方法**：
- 使用validaterawtransaction接口
- 调用simulateTransaction预检查
- 通过mempool验证机制

**记忆要点**：格式、签名、余额、业务逻辑、网络规则

**总结**：RPC节点可以全面验证交易合法性，从格式到签名到余额，确保交易符合所有规则。

## 102. 如何节省gas费？从数据结构设计的角度展开说说？

从数据结构角度节省gas费有**5个优化策略**：

**策略1 - 变量打包**：
- 将多个小变量打包到一个storage slot
- uint256占用32字节，可以拆分成多个小类型
- 如：uint128 + uint128 = 一个slot

**策略2 - 数据类型优化**：
- 使用最小够用的数据类型
- uint8比uint256便宜
- bool比uint256便宜

**策略3 - 存储位置选择**：
- storage：永久存储，最贵
- memory：临时存储，中等
- calldata：只读参数，最便宜

**策略4 - 数组和映射优化**：
- 定长数组比动态数组便宜
- mapping比array查找更便宜
- 避免不必要的数组长度变化

**策略5 - 结构体设计**：
- 合理排列结构体成员顺序
- 避免存储间隙浪费
- 使用事件代替存储记录日志

**实际例子**：
```solidity
// 优化前：3个storage slot
uint256 a;
uint8 b;
uint256 c;

// 优化后：2个storage slot
uint256 a;
uint256 c;
uint8 b;
```

**记忆要点**：变量打包、类型优化、存储选择、数组映射、结构体设计

**总结**：节省gas主要是合理设计数据结构，减少storage操作，优化变量打包和类型选择。

## 103. 调用ec2的服务器被黑了咋办

EC2服务器被黑的**4步应急处理**：

**步骤1 - 立即隔离**：
- 断开服务器网络连接
- 停止所有对外服务
- 防止攻击者继续操作

**步骤2 - 损失评估**：
- 检查是否有资金损失
- 确认数据泄露范围
- 分析攻击者获取的权限

**步骤3 - 系统恢复**：
- 从干净的备份恢复系统
- 更换所有密钥和密码
- 修复安全漏洞

**步骤4 - 加强防护**：
- 升级安全补丁
- 加强访问控制
- 部署入侵检测系统

**预防措施**：
- 定期安全扫描和更新
- 最小权限原则
- 多重身份验证
- 网络隔离和防火墙

**关键原则**：
- 速度第一，先止损再分析
- 假设最坏情况，全面检查
- 从根本上修复，不留后门

**记忆要点**：隔离、评估、恢复、加强

**总结**：服务器被黑要立即断网隔离，评估损失后从干净备份恢复，然后全面加强安全防护。

## 104.温钱包，多签（你们温钱包怎么实现的）

## 105.你们这个签名机方案如果是业务层被端了，你们咋办

## 106. 讲一下cosmos的特点

Cosmos有**4个核心特点**：

**特点1 - 模块化架构**：
- Cosmos SDK提供模块化开发框架
- 开发者可以快速搭建专用区块链
- 模块可以复用和组合

**特点2 - 跨链互操作**：
- IBC协议实现链间通信
- 资产可以在不同链之间转移
- 构建区块链互联网

**特点3 - 主权独立**：
- 每条链都有自己的治理机制
- 可以自定义共识算法和参数
- 不依赖其他链的安全性

**特点4 - 高性能共识**：
- Tendermint BFT共识算法
- 即时确定性，无需等待确认
- 高吞吐量和低延迟

**生态优势**：
- Hub-Zone架构连接多条链
- 丰富的DeFi生态
- 活跃的开发者社区

**应用案例**：
- Osmosis（DEX）
- Terra（稳定币）
- Akash（云计算）

**记忆要点**：模块化、跨链、主权、高性能

**总结**：Cosmos最大特点是模块化开发和跨链互操作，让每条链都能保持独立性又能互相连接。

## 107. 说明下solana的ata账户

Solana的ATA账户有**4个关键特性**：

**特性1 - 确定性地址**：
- ATA地址通过算法确定性生成
- 基于用户地址+代币mint地址+ATA程序ID
- 相同输入永远产生相同地址

**特性2 - 一对一关系**：
- 每个用户对每种代币只能有一个ATA
- 避免资金分散和管理复杂性
- 简化钱包和DApp集成

**特性3 - 自动创建机制**：
- 首次接收代币时自动创建ATA
- 需要支付约0.002 SOL的租金
- 创建者通常是发送方

**特性4 - 标准化接口**：
- 所有SPL代币使用相同的ATA标准
- 统一的转账和查询接口
- 提高生态兼容性

**地址计算公式**：
```
ATA = findProgramAddress([
  userWallet,
  TOKEN_PROGRAM_ID,
  mintAddress
], ASSOCIATED_TOKEN_PROGRAM_ID)
```

**使用场景**：
- SPL代币转账和接收
- DeFi协议代币操作
- NFT持有和交易

**记忆要点**：确定性地址、一对一关系、自动创建、标准化

**总结**：ATA是Solana的标准代币账户，通过确定性算法保证每个用户每种代币只有一个账户。

## 108. 归集的时候问了下手续费的计算

归集手续费计算有**3个考虑因素**：

**因素1 - 网络费率**：
- 实时获取当前网络的gas价格
- 根据紧急程度选择费率等级
- 批量归集可以使用较低费率

**因素2 - 交易复杂度**：
- 简单转账：固定gas消耗
- 代币转账：需要更多gas
- 批量操作：gas消耗线性增长

**因素3 - 成本效益分析**：
- 归集金额 > 手续费成本才执行
- 设置最小归集金额阈值
- 小额资金可能不值得归集

**计算公式**：
- BTC：交易大小(vB) × 费率(sat/vB)
- ETH：gasUsed × gasPrice
- 其他链：类似ETH模式

**优化策略**：
- 网络空闲时批量归集
- 使用智能合约批处理
- 动态调整手续费

**实际案例**：
- 归集100个0.01 ETH的地址
- 单笔手续费0.001 ETH
- 总成本0.1 ETH，收益1 ETH

**记忆要点**：网络费率、交易复杂度、成本效益

**总结**：归集手续费要考虑网络费率和交易复杂度，确保归集收益大于手续费成本。

## 109. token 归集的时候需要注意哪些

Token归集需要注意**5个关键问题**：

**问题1 - 主币余额检查**：
- 代币转账需要消耗主币作为gas费
- 确保每个地址有足够的ETH/BNB等主币
- 可能需要先给地址转入gas费

**问题2 - 代币授权处理**：
- 某些代币需要先approve再转账
- 检查当前授权额度是否足够
- 避免重复授权浪费gas

**问题3 - 精度和最小单位**：
- 不同代币有不同的小数位数
- 计算时要考虑代币精度
- 避免精度丢失导致的错误

**问题4 - 特殊代币类型**：
- 通缩代币：转账会扣除手续费
- 重基代币：余额会自动变化
- 暂停代币：可能无法转账

**问题5 - 成本效益计算**：
- 小额代币归集可能不划算
- gas费可能超过代币价值
- 设置合理的归集阈值

**技术实现**：
- 批量查询代币余额
- 智能合约批量转账
- 异常处理和重试机制

**记忆要点**：主币余额、代币授权、精度处理、特殊类型、成本效益

**总结**：Token归集比主币复杂，要考虑gas费、授权、精度等问题，还要处理各种特殊代币类型。

## 110. BTC 提现的时候，用哪个账本算法？引出问题：BTC提现的时候，不都是已经归集了吗？为啥还要考虑这个算法

BTC提现账本选择有**3个核心算法**：

**算法1 - 最小找零算法**：
- 选择金额最接近提现金额的UTXO
- 减少找零输出，节省手续费
- 适合精确金额的提现

**算法2 - 合并小额算法**：
- 优先使用小额UTXO
- 清理粉尘资金，整理账本
- 虽然手续费高但长期有益

**算法3 - 手续费优化算法**：
- 根据当前网络费率选择最优组合
- 平衡手续费和找零数量
- 动态调整策略

**为什么归集后还需要算法**：
- 归集不是100%完整的，总有新的充值
- 大额提现可能需要多个UTXO组合
- 不同面额的UTXO适合不同提现金额
- 手续费优化需要智能选择

**实际场景**：
- 用户提现0.5 BTC，有0.3和0.4两个UTXO
- 选择0.3+0.4组合，找零0.2
- 或等待更合适的UTXO

**记忆要点**：最小找零、合并小额、手续费优化

**总结**：即使归集了也要用算法选择最优UTXO组合，平衡手续费成本和资金利用效率。

## 111. 有没有遇到过，提现的时候没有，还有一些用户资金还没有归集，那么你们是如何处理的呢？

提现时资金未归集的**4种处理方案**：

**方案1 - 实时归集**：
- 检测到提现请求时立即归集相关地址
- 归集完成后再执行提现
- 用户等待时间稍长但能成功提现

**方案2 - 预留资金池**：
- 热钱包保持一定资金储备
- 先用储备资金满足提现
- 后台异步进行资金归集

**方案3 - 智能路由**：
- 系统自动寻找有足够余额的地址
- 直接从用户充值地址提现
- 避免不必要的归集操作

**方案4 - 延迟处理**：
- 将提现请求加入队列
- 等待下次归集完成后处理
- 给用户明确的等待时间预期

**技术实现**：
- 实时余额监控系统
- 智能归集调度算法
- 用户通知和状态更新

**用户体验优化**：
- 提现前余额预检查
- 实时显示处理进度
- 异常情况及时通知

**记忆要点**：实时归集、预留资金、智能路由、延迟处理

**总结**：遇到资金未归集时，优先考虑实时归集或用储备资金，保证用户提现体验。

## 112. 你们钱包和风控的交互是咋么样的？请具体描述一下

钱包与风控系统交互有**4个关键环节**：

**环节1 - 实时风险检测**：
- 每笔交易前调用风控接口
- 传递用户信息、交易金额、目标地址
- 风控返回风险等级和处理建议

**环节2 - 分级处理机制**：
- 低风险：直接通过，正常处理
- 中风险：增加验证步骤（短信、邮箱）
- 高风险：人工审核或直接拒绝

**环节3 - 异常行为监控**：
- 风控系统持续监控用户行为
- 检测异常登录、大额转账、频繁操作
- 触发异常时主动冻结账户

**环节4 - 数据反馈循环**：
- 钱包向风控反馈交易结果
- 风控根据结果调整风险模型
- 持续优化风险识别准确率

**交互接口**：
- checkRisk()：风险评估接口
- reportTransaction()：交易上报接口
- getUserRiskLevel()：用户风险等级查询

**数据流向**：
钱包 → 风控评估 → 处理决策 → 执行结果 → 数据反馈

**记忆要点**：实时检测、分级处理、异常监控、数据反馈

**总结**：钱包和风控是实时交互的，每笔交易都要过风控检查，根据风险等级采取不同处理策略。


