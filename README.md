# ZeroGas - 基于 EIP-7002 的 0 Gas 费交易系统

## 项目概述

ZeroGas 是一个创新的区块链解决方案，通过结合 Meta Transaction (EIP-2771) 和智能 Gas 费补贴机制，实现真正的 0 Gas 费用户体验。虽然 EIP-7002 本身主要用于验证者退出，但我们巧妙地利用其系统调用特性来优化 Gas 费管理。

## 🎯 核心特性

- **真正的 0 Gas 费体验**：用户无需持有 ETH 即可执行交易
- **Meta Transaction 支持**：基于 EIP-2771 标准的安全实现
- **智能 Gas 费补贴**：通过预付费池和 Relayer 网络分担成本
- **多层安全防护**：防重放攻击、权限控制、速率限制
- **易于集成**：提供完整的 SDK 和 API
- **去中心化 Relayer**：支持多个 Relayer 提供冗余服务

## 🏗️ 技术架构

```
用户 (无需 ETH) 
    ↓ 签名 Meta Transaction
Relayer 网络 
    ↓ 代付 Gas 费并执行
智能合约 
    ↓ 验证签名并执行逻辑
Gas 费补贴池 
    ↓ 补偿 Relayer
```

## 📁 项目结构

```
├── contracts/              # 智能合约
│   ├── MetaTransactionBase.sol    # Meta Transaction 基础合约
│   ├── ZeroGasManager.sol         # 0 Gas 费管理合约
│   ├── ZeroGasDemo.sol           # 演示合约
│   └── RelayerService.sol        # Relayer 服务合约
├── sdk/                    # JavaScript SDK
│   └── ZeroGasSDK.js
├── relayer/               # Relayer 服务
│   └── server.js
├── scripts/               # 部署脚本
│   └── deploy.js
├── test/                  # 测试文件
│   └── ZeroGasSystem.test.js
├── examples/              # 使用示例
│   └── usage-example.js
└── analysis/              # 技术分析
    └── eip7002_analysis.md
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 部署合约

```bash
# 启动本地区块链
npx hardhat node

# 部署合约
npx hardhat run scripts/deploy.js --network localhost
```

### 3. 启动 Relayer 服务

```bash
cd relayer
PRIVATE_KEY=your_private_key npm start
```

### 4. 使用 SDK

```javascript
const ZeroGasSDK = require('./sdk/ZeroGasSDK');

const sdk = new ZeroGasSDK({
    rpcUrl: 'http://localhost:8545',
    relayerUrl: 'http://localhost:3000',
    contractAddress: '0x...',
    chainId: 1337
});

await sdk.initialize(contractABI);
sdk.setSigner(userSigner);

// 执行 0 Gas 费交易
const result = await sdk.executeZeroGasTransaction('updateProfile', ['Alice', 100]);
```

## 💡 核心概念

### Meta Transaction

Meta Transaction 允许用户签名交易但不直接发送到区块链，而是通过第三方 Relayer 代为执行：

1. **用户签名**：用户使用私钥签名交易数据
2. **Relayer 执行**：Relayer 代为发送交易并支付 Gas 费
3. **合约验证**：智能合约验证原始签名并执行逻辑

### Gas 费补贴机制

通过多种方式实现 Gas 费补贴：

- **预付费池**：项目方或用户预存资金
- **代币激励**：通过代币奖励覆盖 Gas 费
- **第三方赞助**：广告商或合作伙伴赞助
- **批量优化**：通过批量处理降低单笔成本

### 安全机制

- **Nonce 管理**：防止重放攻击
- **签名验证**：确保交易来源可信
- **权限控制**：只有授权 Relayer 可执行
- **速率限制**：防止滥用和攻击
- **Gas 限制**：控制单笔交易最大消耗

## 🔧 配置选项

### SDK 配置

```javascript
const config = {
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
    relayerUrl: 'https://api.zerogas.io',
    contractAddress: '0x...',
    chainId: 1
};
```

### Relayer 配置

```javascript
const config = {
    rpcUrl: process.env.RPC_URL,
    privateKey: process.env.PRIVATE_KEY,
    port: process.env.PORT || 3000,
    chainId: parseInt(process.env.CHAIN_ID),
    allowedOrigins: ['https://yourapp.com']
};
```

## 📊 使用统计

每个用户默认享有：
- **免费交易**：每日 10 次免费交易
- **VIP 用户**：每日 100 次免费交易
- **Gas 限制**：单笔交易最大 500,000 Gas
- **速率限制**：每 15 分钟最多 100 个请求

## 🧪 测试

```bash
# 运行所有测试
npx hardhat test

# 运行特定测试
npx hardhat test test/ZeroGasSystem.test.js

# 查看测试覆盖率
npx hardhat coverage
```

## 🔍 监控和调试

### 健康检查

```bash
curl http://localhost:3000/health
```

### 获取 Relayer 信息

```bash
curl http://localhost:3000/api/relayer/info
```

### 查看交易状态

```bash
curl http://localhost:3000/api/transaction/0x...
```

## 🚨 安全考虑

1. **私钥管理**：Relayer 私钥必须安全存储
2. **资金监控**：定期检查 Gas 池余额
3. **异常检测**：监控异常交易模式
4. **访问控制**：限制 API 访问来源
5. **备份机制**：多个 Relayer 提供冗余

## 🛣️ 发展路线图

- [x] 基础 Meta Transaction 实现
- [x] Gas 费补贴机制
- [x] Relayer 服务
- [x] JavaScript SDK
- [ ] 多链支持
- [ ] 去中心化 Relayer 网络
- [ ] 高级 Gas 优化
- [ ] 图形化管理界面

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [OpenZeppelin](https://openzeppelin.com/) - 安全的智能合约库
- [Biconomy](https://biconomy.io/) - Meta Transaction 灵感来源
- [EIP-2771](https://eips.ethereum.org/EIPS/eip-2771) - Meta Transaction 标准
- [EIP-7002](https://eips.ethereum.org/EIPS/eip-7002) - 系统调用机制参考

## 📞 联系我们

- 项目主页：[https://github.com/yourorg/zerogas](https://github.com/yourorg/zerogas)
- 文档：[https://docs.zerogas.io](https://docs.zerogas.io)
- 社区：[https://discord.gg/zerogas](https://discord.gg/zerogas)

---

**注意**：本项目仍在开发中，请勿在生产环境中使用未经充分测试的代码。
