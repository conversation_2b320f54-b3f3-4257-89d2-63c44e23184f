/**
 * ZeroGas Relayer Server
 * 提供 Meta Transaction 中继服务
 */

const express = require('express');
const cors = require('cors');
const { ethers } = require('ethers');
const rateLimit = require('express-rate-limit');

class RelayerServer {
    constructor(config) {
        this.config = config;
        this.app = express();
        this.provider = new ethers.providers.JsonRpcProvider(config.rpcUrl);
        this.wallet = new ethers.Wallet(config.privateKey, this.provider);
        this.contracts = new Map();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    setupMiddleware() {
        // CORS 配置
        this.app.use(cors({
            origin: this.config.allowedOrigins || '*',
            methods: ['GET', 'POST'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));

        // JSON 解析
        this.app.use(express.json({ limit: '10mb' }));

        // 速率限制
        const limiter = rateLimit({
            windowMs: 15 * 60 * 1000, // 15 分钟
            max: 100, // 每个 IP 最多 100 个请求
            message: 'Too many requests from this IP'
        });
        this.app.use('/api/', limiter);

        // 请求日志
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                relayer: this.wallet.address,
                balance: null // 将在响应中异步填充
            });
        });

        // 获取 Relayer 信息
        this.app.get('/api/relayer/info', async (req, res) => {
            try {
                const balance = await this.wallet.getBalance();
                const gasPrice = await this.provider.getGasPrice();
                
                res.json({
                    address: this.wallet.address,
                    balance: ethers.utils.formatEther(balance),
                    gasPrice: ethers.utils.formatUnits(gasPrice, 'gwei'),
                    chainId: (await this.provider.getNetwork()).chainId,
                    supportedContracts: Array.from(this.contracts.keys())
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // 执行 Meta Transaction
        this.app.post('/api/execute', async (req, res) => {
            try {
                const result = await this.executeMetaTransaction(req.body);
                res.json(result);
            } catch (error) {
                console.error('Execute error:', error);
                res.status(400).json({ error: error.message });
            }
        });

        // 批量执行 Meta Transactions
        this.app.post('/api/execute/batch', async (req, res) => {
            try {
                const results = await this.executeBatchMetaTransactions(req.body);
                res.json(results);
            } catch (error) {
                console.error('Batch execute error:', error);
                res.status(400).json({ error: error.message });
            }
        });

        // 估算 Gas 费用
        this.app.post('/api/estimate', async (req, res) => {
            try {
                const estimate = await this.estimateGas(req.body);
                res.json(estimate);
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });

        // 获取交易状态
        this.app.get('/api/transaction/:hash', async (req, res) => {
            try {
                const receipt = await this.provider.getTransactionReceipt(req.params.hash);
                res.json({
                    hash: req.params.hash,
                    status: receipt ? (receipt.status === 1 ? 'success' : 'failed') : 'pending',
                    blockNumber: receipt?.blockNumber,
                    gasUsed: receipt?.gasUsed?.toString()
                });
            } catch (error) {
                res.status(400).json({ error: error.message });
            }
        });

        // 获取推荐的 Relayer
        this.app.get('/api/relayers/recommended', (req, res) => {
            res.json({
                relayer: this.wallet.address,
                endpoint: this.config.endpoint,
                reputation: 100,
                successRate: 99.5
            });
        });
    }

    setupErrorHandling() {
        this.app.use((error, req, res, next) => {
            console.error('Unhandled error:', error);
            res.status(500).json({ error: 'Internal server error' });
        });
    }

    async executeMetaTransaction(txData) {
        const {
            chainId,
            contractAddress,
            userAddress,
            functionSignature,
            signature,
            gasLimit = 500000
        } = txData;

        // 验证链 ID
        const networkChainId = (await this.provider.getNetwork()).chainId;
        if (chainId !== networkChainId) {
            throw new Error(`Invalid chain ID. Expected ${networkChainId}, got ${chainId}`);
        }

        // 获取合约实例
        const contract = this.getContract(contractAddress);
        if (!contract) {
            throw new Error('Contract not supported');
        }

        // 验证 Gas 限制
        if (gasLimit > 1000000) {
            throw new Error('Gas limit too high');
        }

        // 检查用户是否可以执行免费交易
        const canExecute = await contract.canUserExecuteFreeTransaction(userAddress);
        if (!canExecute) {
            throw new Error('User has no free transactions left');
        }

        // 检查 Gas 池是否有足够资金
        const estimatedCost = gasLimit * (await this.provider.getGasPrice());
        const canCover = await contract.canGasPoolCover(estimatedCost);
        if (!canCover) {
            throw new Error('Gas pool insufficient');
        }

        // 执行交易
        const tx = await contract.executeZeroGasTransaction(
            userAddress,
            functionSignature,
            signature.r,
            signature.s,
            signature.v,
            gasLimit,
            {
                gasLimit: gasLimit + 100000, // 额外的 Gas 用于合约执行
                gasPrice: await this.provider.getGasPrice()
            }
        );

        console.log(`Transaction submitted: ${tx.hash}`);

        return {
            success: true,
            transactionHash: tx.hash,
            relayer: this.wallet.address,
            timestamp: new Date().toISOString()
        };
    }

    async executeBatchMetaTransactions(batchData) {
        const { transactions } = batchData;
        const results = [];

        for (const txData of transactions) {
            try {
                const result = await this.executeMetaTransaction(txData);
                results.push(result);
                
                // 添加延迟避免 nonce 冲突
                await this.delay(1000);
            } catch (error) {
                results.push({
                    success: false,
                    error: error.message
                });
            }
        }

        return {
            success: true,
            results: results,
            totalTransactions: transactions.length,
            successfulTransactions: results.filter(r => r.success).length
        };
    }

    async estimateGas(txData) {
        const {
            contractAddress,
            userAddress,
            functionSignature,
            signature
        } = txData;

        const contract = this.getContract(contractAddress);
        if (!contract) {
            throw new Error('Contract not supported');
        }

        try {
            const gasEstimate = await contract.estimateGas.executeZeroGasTransaction(
                userAddress,
                functionSignature,
                signature.r,
                signature.s,
                signature.v,
                500000
            );

            const gasPrice = await this.provider.getGasPrice();
            const gasCost = gasEstimate.mul(gasPrice);

            return {
                gasLimit: gasEstimate.toString(),
                gasPrice: ethers.utils.formatUnits(gasPrice, 'gwei'),
                gasCost: ethers.utils.formatEther(gasCost),
                gasCostWei: gasCost.toString()
            };
        } catch (error) {
            throw new Error(`Gas estimation failed: ${error.message}`);
        }
    }

    getContract(address) {
        if (!this.contracts.has(address)) {
            // 这里应该根据地址加载相应的合约 ABI
            // 为了简化，我们假设所有合约都使用相同的 ABI
            const abi = require('./abis/ZeroGasDemo.json'); // 需要提供 ABI 文件
            const contract = new ethers.Contract(address, abi, this.wallet);
            this.contracts.set(address, contract);
        }
        return this.contracts.get(address);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async start() {
        const port = this.config.port || 3000;
        
        // 检查钱包余额
        const balance = await this.wallet.getBalance();
        console.log(`Relayer address: ${this.wallet.address}`);
        console.log(`Relayer balance: ${ethers.utils.formatEther(balance)} ETH`);
        
        if (balance.lt(ethers.utils.parseEther("0.1"))) {
            console.warn('⚠️  Warning: Relayer balance is low!');
        }

        this.app.listen(port, () => {
            console.log(`🚀 Relayer server started on port ${port}`);
            console.log(`📡 Health check: http://localhost:${port}/health`);
            console.log(`🔗 Chain ID: ${this.config.chainId}`);
        });
    }
}

// 配置和启动服务器
if (require.main === module) {
    const config = {
        rpcUrl: process.env.RPC_URL || 'http://localhost:8545',
        privateKey: process.env.PRIVATE_KEY,
        port: process.env.PORT || 3000,
        chainId: parseInt(process.env.CHAIN_ID) || 1337,
        allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['*'],
        endpoint: process.env.RELAYER_ENDPOINT || 'http://localhost:3000'
    };

    if (!config.privateKey) {
        console.error('❌ PRIVATE_KEY environment variable is required');
        process.exit(1);
    }

    const server = new RelayerServer(config);
    server.start().catch(console.error);
}

module.exports = RelayerServer;
