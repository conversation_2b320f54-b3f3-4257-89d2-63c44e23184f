# 区块链交易解析面试题大全

## Ethereum 交易解析面试题

### 基础概念类

#### 1. 如何判断一笔以太坊交易是否成功？有哪些状态码？

这个问题我在开发钱包的时候经常遇到。主要通过 `eth_getTransactionReceipt` 接口的 `status` 字段来判断：

- `status: "0x1"` 或 `status: 1`：交易执行成功
- `status: "0x0"` 或 `status: 0`：交易执行失败

需要注意的是，即使交易失败（status=0），仍然会消耗gas费用，这在计算用户手续费时要特别注意。

我们当时在钱包里还会检查 `blockNumber` 字段：
- `blockNumber: null`：交易还在pending状态
- `blockNumber: 有值`：交易已经被打包进区块

#### 2. ERC20代币转账和ETH转账在交易结构上有什么区别？

这个区别我在解析交易时经常用到：

**ETH转账特征：**
- `to` 字段：接收方地址
- `value` 字段：转账金额（wei单位）
- `input` 字段：为空（"0x"）或很短

**ERC20代币转账特征：**
- `to` 字段：代币合约地址
- `value` 字段：通常为0（不转ETH）
- `input` 字段：包含方法调用数据
  - 前4字节：`0xa9059cbb`（transfer方法签名）
  - 接下来32字节：接收方地址
  - 最后32字节：转账金额

实际转账信息要从 `logs` 中的Transfer事件解析：
```
topics[0]: 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef
topics[1]: from地址（补齐32字节）
topics[2]: to地址（补齐32字节）
data: 转账金额（hex编码）
```

#### 3. 如何从交易数据中识别出是ERC20还是ERC721转账？

这个问题我在开发NFT功能时深入研究过：

**共同点：**
- 都使用相同的Transfer事件签名
- 都有from、to参数

**区别方法：**

1. **通过topics数量判断：**
   - ERC20：3个topics（事件签名 + from + to）
   - ERC721：4个topics（事件签名 + from + to + tokenId）

2. **通过合约接口判断：**
   - 调用 `supportsInterface(0x80ac58cd)` 检查是否支持ERC721
   - 调用 `decimals()` 方法，ERC721通常不支持或返回0

3. **通过数据特征判断：**
   - ERC20的amount通常是大数值
   - ERC721的tokenId通常是较小的整数

我们当时维护了一个合约类型缓存，避免重复查询。

### 实际应用类

#### 4. 在钱包系统中，如何准确识别用户的充值交易？

这是钱包系统的核心逻辑，我们的判断流程是：

**ETH充值识别：**
```javascript
if (transaction.to in 系统用户地址库) {
    交易类型 = "ETH充值"
    用户ID = 地址映射表[transaction.to]
    充值金额 = transaction.value
}
```

**ERC20充值识别：**
```javascript
// 遍历交易收据中的logs
for (log of receipt.logs) {
    if (log.topics[0] === ERC20_TRANSFER_SIGNATURE) {
        const toAddress = '0x' + log.topics[2].slice(26)
        if (toAddress in 系统用户地址库) {
            交易类型 = "ERC20充值"
            用户ID = 地址映射表[toAddress]
            代币合约 = log.address
            充值金额 = parseInt(log.data, 16)
        }
    }
}
```

**关键注意点：**
- 要检查交易是否成功（status=1）
- 要达到足够的确认数才入账
- 要防止重复处理同一笔交易
- 要处理合约调用产生的多个Transfer事件

#### 5. 如何处理交易失败但仍然消耗Gas的情况？

这个问题我们在成本核算时遇到过：

**失败交易的特征：**
- `receipt.status = "0x0"`
- `receipt.gasUsed > 0`
- 可能有部分logs（执行到一半失败）

**处理策略：**
1. **手续费计算：** 仍需计算 `gasUsed * gasPrice` 的费用
2. **余额处理：** 不更新代币余额，但要扣除ETH手续费
3. **用户通知：** 明确告知用户交易失败原因
4. **错误分析：** 记录失败原因，优化交易参数

**常见失败原因：**
- Gas不足
- 余额不足
- 合约执行revert
- nonce错误

#### 6. 智能合约调用失败时，如何从日志中获取具体的错误信息？

这个我在调试DeFi交易时经常用到：

**方法1：解析Revert事件**
```javascript
// 查找Error或Revert相关的事件
const errorLogs = receipt.logs.filter(log => 
    log.topics[0] === ERROR_EVENT_SIGNATURE
);
```

**方法2：使用debug_traceTransaction**
```javascript
const trace = await web3.eth.debug.traceTransaction(txHash, {
    tracer: 'callTracer'
});
// trace.output 包含revert信息
```

**方法3：解析revert reason**
```javascript
if (receipt.status === '0x0') {
    try {
        await web3.eth.call(transaction, transaction.blockNumber);
    } catch (error) {
        // error.message 包含revert原因
        console.log('Revert reason:', error.message);
    }
}
```

我们当时还维护了一个常见错误码的映射表，给用户显示友好的错误信息。

### 性能优化类

#### 7. 如何批量解析大量以太坊交易以提高效率？

这个问题我在处理历史数据时深有体会：

**批量查询策略：**

1. **使用eth_getLogs批量获取事件：**
```javascript
const logs = await web3.eth.getPastLogs({
    fromBlock: startBlock,
    toBlock: endBlock,
    topics: [ERC20_TRANSFER_SIGNATURE],
    address: tokenContracts
});
```

2. **批量RPC调用：**
```javascript
const batch = new web3.BatchRequest();
transactions.forEach(tx => {
    batch.add(web3.eth.getTransactionReceipt.request(tx.hash));
});
const results = await batch.execute();
```

3. **并发控制：**
```javascript
const limit = pLimit(10); // 限制并发数
const promises = txHashes.map(hash => 
    limit(() => parseTransaction(hash))
);
const results = await Promise.all(promises);
```

**优化技巧：**
- 按区块范围分批处理，避免超时
- 缓存常用数据（ABI、代币信息）
- 使用专业节点服务（Infura、Alchemy）
- 实现重试机制处理网络错误

#### 8. 在解析历史交易时，如何处理区块重组问题？

区块重组是我们系统稳定性的重要考虑：

**监控策略：**
```javascript
// 定期检查最新区块
const latestBlock = await web3.eth.getBlockNumber();
const ourLatestBlock = await getOurLatestBlock();

if (latestBlock < ourLatestBlock) {
    // 发生了重组，需要回滚
    await handleReorg(latestBlock);
}
```

**处理流程：**
1. **检测重组：** 对比本地和链上的区块哈希
2. **回滚数据：** 删除被重组区块的交易记录
3. **重新解析：** 从分叉点重新解析交易
4. **通知用户：** 更新受影响的交易状态

**预防措施：**
- 设置安全确认数（通常6-12个确认）
- 定期验证历史区块哈希
- 实现自动回滚和重新解析机制

## Bitcoin 交易解析面试题

### UTXO模型理解

#### 9. 比特币的UTXO模型与以太坊的账户模型有什么本质区别？

这个问题体现了对区块链底层机制的理解：

**UTXO模型（比特币）：**
- 每个输出都是一次性的，花费后就消失
- 没有账户余额概念，余额是所有UTXO的总和
- 交易是UTXO的消费和创建过程
- 天然支持并行处理

**账户模型（以太坊）：**
- 账户有持续的状态和余额
- 交易是账户间的状态转换
- 需要nonce防止重放攻击
- 状态更新是串行的

**实际影响：**
- UTXO模型更适合简单转账，隐私性更好
- 账户模型更适合智能合约，编程更直观
- UTXO需要管理找零，账户模型不需要

#### 10. 如何计算比特币交易的手续费？

这是钱包开发的基础知识：

**计算公式：**
```
手续费 = 总输入金额 - 总输出金额
```

**具体实现：**
```javascript
async function calculateFee(txid) {
    const tx = await bitcoinRPC.getRawTransaction(txid, true);
    
    let totalInput = 0;
    let totalOutput = 0;
    
    // 计算输入总额
    for (const vin of tx.vin) {
        if (vin.coinbase) {
            // Coinbase交易没有输入费用
            return 0;
        }
        
        const prevTx = await bitcoinRPC.getRawTransaction(vin.txid, true);
        totalInput += prevTx.vout[vin.vout].value;
    }
    
    // 计算输出总额
    for (const vout of tx.vout) {
        totalOutput += vout.value;
    }
    
    return totalInput - totalOutput;
}
```

**特殊情况：**
- Coinbase交易（挖矿奖励）：手续费为0
- 隔离见证交易：按虚拟大小计算费率
- RBF交易：可能被更高手续费的交易替换

#### 11. 什么是隔离见证交易？在解析时需要注意什么？

隔离见证（SegWit）是比特币的重要升级：

**技术原理：**
- 将签名数据（witness）从交易主体中分离
- 减少交易大小，提高网络容量
- 修复交易延展性问题

**解析要点：**

1. **识别SegWit交易：**
```javascript
// 检查是否有witness数据
const isSegWit = tx.vin.some(input => 
    input.txinwitness && input.txinwitness.length > 0
);
```

2. **计算虚拟大小：**
```javascript
// SegWit交易使用weight计算费率
const vsize = Math.ceil(tx.weight / 4);
const feeRate = fee / vsize; // sat/vB
```

3. **地址格式：**
- P2WPKH：bc1开头的Bech32地址
- P2WSH：bc1开头的长地址
- P2SH-P2WPKH：3开头的兼容地址

**实际影响：**
- 手续费计算更复杂
- 地址验证需要支持新格式
- 交易大小计算要用虚拟大小
