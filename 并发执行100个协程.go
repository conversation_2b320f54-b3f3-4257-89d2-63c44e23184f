package main

import (
	"fmt"
	"sync"
	"sync/atomic"
)

var (
	counters = 0
	mu       sync.Mutex
)

func incrementCounter(wg *sync.WaitGroup) {
	defer wg.Done()
	mu.Lock()
	counters++
	mu.Unlock()
}

func main01() {
	var wg sync.WaitGroup
	goroutineCount := 1000

	// 启动100个goroutine 并发执行 incrementCounter
	wg.Add(goroutineCount)

	for i := 0; i < goroutineCount; i++ {
		go incrementCounter(&wg)
	}

	wg.Wait()
	fmt.Println("Final Counter:", counters)

}

var counter001 int64 = 0 // 使用 int64 以便原子操作

func increment001(wg *sync.WaitGroup) {
	defer wg.Done() // 去掉这个也会导致死锁
	atomic.AddInt64(&counter001, 1)
}

func main() {
	var wg sync.WaitGroup
	goroutineCount := 1000
	wg.Add(goroutineCount)
	for i := 0; i < goroutineCount; i++ {
		go increment001(&wg)
	}
	wg.Wait()
	fmt.Println("Final Counter001:", counter001)
}
