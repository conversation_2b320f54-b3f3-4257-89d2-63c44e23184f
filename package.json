{"name": "zerogas-system", "version": "1.0.0", "description": "基于 EIP-7002 的 0 Gas 费交易系统", "main": "index.js", "directories": {"example": "examples", "test": "test"}, "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:coverage": "hardhat coverage", "deploy:local": "hardhat run scripts/deploy.js --network localhost", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "deploy:mumbai": "hardhat run scripts/deploy.js --network mumbai", "node": "hardhat node", "clean": "hardhat clean", "relayer:start": "cd relayer && node server.js", "example:basic": "node examples/usage-example.js", "verify:sepolia": "hardhat verify --network sepolia", "size": "hardhat size-contracts"}, "keywords": ["ethereum", "blockchain", "meta-transaction", "gasless", "eip-2771", "eip-7002", "defi"], "author": "ZeroGas Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.0", "@openzeppelin/contracts": "^4.9.0", "hardhat": "^2.17.0", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.4"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "express-rate-limit": "^6.8.1", "ethers": "^5.7.2", "dotenv": "^16.3.1"}, "engines": {"node": ">=16.0.0"}}