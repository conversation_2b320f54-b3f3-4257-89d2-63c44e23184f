package main

import (
	"fmt"
	"sync"
	"sync/atomic"
)

var counter = 0      //共享变量
var mutex sync.Mutex // 定义互斥锁

var counter01 int64 = 0 // 使用int64以便atomic 操作

func increment(wg *sync.WaitGroup) {
	defer wg.Done()
	for i := 0; i < 1000; i++ {
		// 加锁
		mutex.Lock()   // 上锁
		counter++      // 数据竞争
		mutex.Unlock() // 解锁
	}
}

func increment01(wg *sync.WaitGroup) {
	defer wg.Done()
	for i := 0; i < 1000; i++ {
		atomic.AddInt64(&counter01, 1) // 使用原子加操作
	}
}

func main() {
	var wg sync.WaitGroup
	wg.Add(2)

	// 方式一 ： 加锁
	//go increment(&wg)
	//go increment(&wg)

	// 方式二 ： 原子加操作，避免数据竞争，也避免了锁开销
	go increment01(&wg)
	go increment01(&wg)

	wg.Wait()
	fmt.Println(counter01)
}
