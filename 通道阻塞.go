package main

import (
	"fmt"
	"time"
)

// 生产者
func producer(ch chan int) {
	for i := 0; i < 5; i++ {
		//fmt.Println("Producing ", i)
		//// ch 是无缓冲通过，需要等待消费者从通道接收数据后才能继续发送，因此会频繁阻塞在 ch<-i 这一行，等待消费者处理完数据
		//ch <- i // 生产者发送数据到通道
		//fmt.Println("Produced ", i)

		select {
		case ch <- 1:
			fmt.Println("producer", i)
		case <-time.After(1 * time.Second):
			fmt.Println("timeout")
		}

	}
	close(ch) // 生产者完成后关闭通道
}

// 消费者
func consumer(ch chan int) {
	for item := range ch {
		time.Sleep(time.Second * 2) // 模拟较慢的消费速度
		fmt.Println("Consumed ", item)
	}
}

func main() {
	ch := make(chan int)
	go producer(ch)
	go consumer(ch)

	time.Sleep(time.Second * 12)
	fmt.Println("Main goroutine done")
}

// 生产者的速度受限于消费者的消费速度，因为生产者在每次发送时都需要等待消费者接收，造成通道阻塞。

// 解决方案一 ： 使用带缓冲通道
// ch := make(chan int, 3)

// 解决方案二： 增加消费者数量
/*
	for i := 0; i < 3 ; i++ {
		go consumer(ch)
	}
*/

// 解决方案三 ： 使用select 加入超时机制
/*
	select {
    case ch <- i:
		fmt.Println("Produced:", i)
	case <-time.After(1 * time.Second):
		fmt.Println("Producer timed out")
	}

select 每次只执行一个分支；
哪个 case 可以先执行，就走哪个；
如果多个 case 同时准备好，Go 会随机选择一个；
超时分支可以防止一直阻塞，提高程序健壮性；
所以你看到的“只走一个，不是两个一起走”。
*/
