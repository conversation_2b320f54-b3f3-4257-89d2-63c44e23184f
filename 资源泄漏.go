package main

import (
	"context"
	"fmt"
	"time"
)

func process(ch <-chan int) {
	for {
		// 无限等待接收数据
		value := <-ch
		fmt.Println(value)
	}
}

func main01() {
	ch := make(chan int)

	// 启动 goroutine
	go process(ch)

	// 模拟一些工作
	time.Sleep(time.Second * 20)

	// main 函数结束，但 process goroutine 仍然在等待通道的数据，无法退出
	fmt.Println("Main function done")
}

// 解决方式一： 使用context 控制 goroutine 生命周期
func process1(ctx context.Context, ch <-chan int) {
	for {
		select {
		case value := <-ch:
			fmt.Println(value)
		case <-ctx.Done(): // 检查context 的取消信息
			fmt.Println("Context done")
			return
		}
	}
}

func main02() {
	ch := make(chan int)
	ctx, cancel := context.WithCancel(context.Background())

	// 启动 goroutine
	go process1(ctx, ch)

	// 模拟一些操作
	time.Sleep(time.Second * 10)

	// 停止 goroutine
	cancel()

	// 等待 goroutine 退出
	time.Sleep(time.Second * 1)
	fmt.Println("Main function done")
}

// 解决方式二：通道方式

func main() {
	ch := make(chan int)
	go process(ch)

	// 发送数据
	ch <- 1
	ch <- 2

	// 关闭通道，通知goroutine 退出
	close(ch)

	// 等待 goroutine 处理完
	time.Sleep(time.Second * 1)
	fmt.Println("Main function done")

}

// 解决方式三：runtime.NumGoroutine() 监控goroutine数量，配合 pprof 工具检测
