package main

import (
	"fmt"
)
import "unsafe"

type Example1 struct {
	A byte  // 1 字节
	B int32 // 4 字节
} // 8 字节

type Example2 struct {
	A string // 16 字节
	B bool   // 1 字节
} // 24 字节

type Example3 struct {
	a  float32  // 4 字节
	b  bool     // 1 字节
	c  string   // 16字节
	e  uint32   // 4字节
	f  float64  // 8 字节
	ts Example2 // 24 字节
} // 40 + 24 = 60 字节

// 应用类型 占 8个字节
type Example4 struct {
	a float32 // 4 字节
	b bool    // 1 字节
	//ba bool      // 1 字节
	c  string    // 16字节
	e  uint32    // 4字节
	f  float64   // 8 字节
	ts *Example2 // 8 字节
} // 6 + 8 = 48 字节

type Example5 struct {
	a float32 // 4 字节
	b bool    // 1 字节
	//ba bool      // 1 字节
	ts []Example2 //  24 字节
	c  string     // 16字节
	e  uint32     // 4字节
	f  float64    // 8 字节

} // 64 字节

type Example6 struct {
	//ts []Example2 // 24 字节
	//ts []uint32 // 切片 24 字节
	//ts []uint64 // 切片 24 字节
	c []string // 切片 24 字节
}

type Example7 struct {
	c [4]uint64 // 32 字节
	//ts []Example2
}

type Example8 struct {
	f float64 // 8 字节
	c string  // 16字节
	a float32 // 4 字节
	e uint32  // 4字节
	b bool    // 1 字节
}

type Example9 struct{} // 0 字节

type Example10 struct {
	//c byte     // 8 字节
	//d []byte   // 24 字节
	//sss comment.Block // 16 字节
	d [][]byte // 24 字节
	a struct{} // 0 字节
	b int64    // 8 字节
}

type Example11 struct {
	A bool  // 1 字节
	B int32 // 4 字节
	C bool  // 1 字节
} // 12 字节

// 优化版
type Example12 struct {
	B int32 // 4 字节
	A bool  // 1 字节
	C bool  // 1 字节
} // 8 字节

func main() {
	fmt.Println(unsafe.Sizeof(Example12{}))
}
