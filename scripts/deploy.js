const { ethers } = require("hardhat");

async function main() {
    console.log("开始部署 0 Gas 费系统合约...");

    // 获取部署账户
    const [deployer] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);
    console.log("账户余额:", ethers.utils.formatEther(await deployer.getBalance()));

    // 1. 部署 ZeroGasDemo 合约
    console.log("\n1. 部署 ZeroGasDemo 合约...");
    const ZeroGasDemo = await ethers.getContractFactory("ZeroGasDemo");
    const zeroGasDemo = await ZeroGasDemo.deploy();
    await zeroGasDemo.deployed();
    console.log("ZeroGasDemo 合约地址:", zeroGasDemo.address);

    // 2. 部署 RelayerService 合约
    console.log("\n2. 部署 RelayerService 合约...");
    const RelayerService = await ethers.getContractFactory("RelayerService");
    const relayerService = await RelayerService.deploy();
    await relayerService.deployed();
    console.log("RelayerService 合约地址:", relayerService.address);

    // 3. 初始化配置
    console.log("\n3. 初始化合约配置...");
    
    // 向 Gas 池存入初始资金
    const initialDeposit = ethers.utils.parseEther("10"); // 10 ETH
    await zeroGasDemo.depositToGasPool({ value: initialDeposit });
    console.log("向 Gas 池存入:", ethers.utils.formatEther(initialDeposit), "ETH");

    // 授权部署者为 Relayer
    await zeroGasDemo.authorizeRelayer(deployer.address, true);
    console.log("授权部署者为 Relayer");

    // 4. 验证部署
    console.log("\n4. 验证部署结果...");
    
    // 检查 Gas 池状态
    const gasPoolStatus = await zeroGasDemo.getGasPoolStatus();
    console.log("Gas 池状态:");
    console.log("- 总存款:", ethers.utils.formatEther(gasPoolStatus.totalDeposits), "ETH");
    console.log("- 可用余额:", ethers.utils.formatEther(gasPoolStatus.availableBalance), "ETH");
    console.log("- 每日限额:", ethers.utils.formatEther(gasPoolStatus.dailyLimit), "ETH");

    // 检查代币余额
    const tokenBalance = await zeroGasDemo.balanceOf(deployer.address);
    console.log("部署者代币余额:", ethers.utils.formatEther(tokenBalance), "ZGT");

    // 5. 测试基本功能
    console.log("\n5. 测试基本功能...");
    
    // 测试更新资料
    await zeroGasDemo.updateProfile("Deployer", 1000);
    const profile = await zeroGasDemo.getProfile(deployer.address);
    console.log("用户资料:", {
        name: profile.name,
        score: profile.score.toString(),
        level: profile.level.toString()
    });

    // 测试免费交易检查
    const canExecute = await zeroGasDemo.canUserExecuteFreeTransaction(deployer.address);
    const remaining = await zeroGasDemo.getUserRemainingTransactions(deployer.address);
    console.log("免费交易状态:");
    console.log("- 可以执行:", canExecute);
    console.log("- 剩余次数:", remaining.toString());

    // 6. 生成配置文件
    const config = {
        network: "localhost", // 或者其他网络
        contracts: {
            ZeroGasDemo: zeroGasDemo.address,
            RelayerService: relayerService.address
        },
        deployer: deployer.address,
        deploymentTime: new Date().toISOString(),
        gasPoolInitialDeposit: ethers.utils.formatEther(initialDeposit),
        chainId: (await ethers.provider.getNetwork()).chainId
    };

    console.log("\n6. 部署配置:");
    console.log(JSON.stringify(config, null, 2));

    // 保存配置到文件
    const fs = require('fs');
    fs.writeFileSync(
        './deployment-config.json', 
        JSON.stringify(config, null, 2)
    );
    console.log("配置已保存到 deployment-config.json");

    console.log("\n✅ 部署完成!");
    console.log("🎉 0 Gas 费系统已成功部署并初始化");
    
    return {
        zeroGasDemo: zeroGasDemo.address,
        relayerService: relayerService.address
    };
}

// 错误处理
main()
    .then((addresses) => {
        console.log("\n📋 合约地址汇总:");
        console.log("ZeroGasDemo:", addresses.zeroGasDemo);
        console.log("RelayerService:", addresses.relayerService);
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ 部署失败:", error);
        process.exit(1);
    });
