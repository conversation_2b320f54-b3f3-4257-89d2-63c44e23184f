package main

import (
	"fmt"
	"sync"
)

func main() {
	var mu1, mu2 sync.Mutex // 定义两个互斥锁
	var wg sync.WaitGroup
	wg.Add(2)

	// 第一个goroutine 尝试按顺序锁定mu1, mu2
	go func() {
		defer wg.Done()
		mu1.Lock()
		fmt.Println("Goroutine 1: locked mu1")

		// 模拟一些操作
		// 等待另一个goroutine锁定mu2
		mu2.Lock()
		fmt.Println("Goroutine 1: locked mu2")
		mu2.Unlock()

		mu1.Unlock()
	}()

	// 第二个 goroutine尝试按顺序锁定mu2 和 mu1
	go func() {
		defer wg.Done()
		mu2.Lock()
		fmt.Println("Goroutine 2: locked mu2")

		// 模拟一些操作
		// 等待另一个goroutine 锁定mu1
		mu1.Lock()
		fmt.Println("Goroutine 2: locked mu1")
		mu1.Unlock()

		mu2.Unlock()
	}()
	wg.Wait()
	fmt.Println("Done")
}

// 解决方式: 两个 goroutine 都先锁定mu1 , 然后锁定 mu2 ，不会出现死锁情况
