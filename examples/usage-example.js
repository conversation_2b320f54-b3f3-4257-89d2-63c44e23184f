/**
 * ZeroGas 系统使用示例
 * 演示如何使用 0 Gas 费功能
 */

const { ethers } = require('ethers');
const ZeroGasSDK = require('../sdk/ZeroGasSDK');

// 合约 ABI（简化版本）
const contractABI = [
    "function updateProfile(string memory name, uint256 score) external",
    "function transfer(address to, uint256 amount) external returns (bool)",
    "function dailyCheckIn() external",
    "function batchOperations(string memory name, uint256 score, address[] memory recipients, uint256[] memory amounts) external",
    "function getNonce(address user) external view returns (uint256)",
    "function canUserExecuteFreeTransaction(address user) external view returns (bool)",
    "function getUserRemainingTransactions(address user) external view returns (uint256)",
    "function getGasPoolStatus() external view returns (uint256, uint256, uint256, uint256, uint256, uint256)",
    "function balanceOf(address user) external view returns (uint256)",
    "function getProfile(address user) external view returns (string, uint256, uint256, uint256)"
];

async function main() {
    console.log("🚀 ZeroGas 系统使用示例");
    console.log("========================");

    // 1. 初始化配置
    const config = {
        rpcUrl: 'http://localhost:8545',
        relayerUrl: 'http://localhost:3000',
        contractAddress: '0x...', // 从部署配置中获取
        chainId: 1337
    };

    // 2. 创建 SDK 实例
    const sdk = new ZeroGasSDK(config);
    await sdk.initialize(contractABI);

    // 3. 创建用户钱包（仅用于签名，不需要 ETH）
    const userWallet = ethers.Wallet.createRandom();
    const provider = new ethers.providers.JsonRpcProvider(config.rpcUrl);
    const signer = userWallet.connect(provider);
    
    sdk.setSigner(signer);

    console.log(`👤 用户地址: ${userWallet.address}`);
    console.log(`🔑 用户私钥: ${userWallet.privateKey}`);
    console.log("注意：用户钱包无需持有 ETH！");

    // 4. 检查用户状态
    console.log("\n📊 检查用户状态...");
    const userStats = await sdk.getUserStats(userWallet.address);
    console.log("用户统计:", userStats);

    // 5. 执行第一个 0 Gas 费交易：更新资料
    console.log("\n🔄 执行第一个 0 Gas 费交易：更新用户资料...");
    try {
        const result1 = await sdk.executeZeroGasTransaction('updateProfile', ['Alice', 150]);
        console.log("✅ 资料更新成功:", result1);
        
        // 等待交易确认
        if (result1.transactionHash) {
            console.log("⏳ 等待交易确认...");
            await sdk.waitForTransaction(result1.transactionHash);
            console.log("✅ 交易已确认");
        }
    } catch (error) {
        console.error("❌ 资料更新失败:", error.message);
    }

    // 6. 执行第二个 0 Gas 费交易：每日签到
    console.log("\n🎯 执行第二个 0 Gas 费交易：每日签到...");
    try {
        const result2 = await sdk.executeZeroGasTransaction('dailyCheckIn', []);
        console.log("✅ 签到成功:", result2);
    } catch (error) {
        console.error("❌ 签到失败:", error.message);
    }

    // 7. 批量执行多个操作
    console.log("\n📦 批量执行多个 0 Gas 费交易...");
    try {
        const batchTransactions = [
            {
                functionName: 'updateProfile',
                params: ['Alice Updated', 200]
            }
            // 可以添加更多交易
        ];

        const batchResult = await sdk.executeBatchZeroGasTransactions(
            batchTransactions,
            { delay: 2000 } // 每个交易间隔 2 秒
        );
        console.log("✅ 批量交易完成:", batchResult);
    } catch (error) {
        console.error("❌ 批量交易失败:", error.message);
    }

    // 8. 检查更新后的状态
    console.log("\n📈 检查更新后的用户状态...");
    const updatedStats = await sdk.getUserStats(userWallet.address);
    console.log("更新后的统计:", updatedStats);

    // 9. 获取 Gas 池状态
    console.log("\n⛽ Gas 池状态...");
    const gasPoolStatus = await sdk.getGasPoolStatus();
    console.log("Gas 池状态:", gasPoolStatus);

    // 10. 演示 Gas 费估算
    console.log("\n💰 估算 Gas 费用...");
    const gasEstimate = await sdk.estimateGasCost('updateProfile', ['Test', 100]);
    console.log("Gas 估算:", gasEstimate);

    console.log("\n🎉 示例完成！");
    console.log("用户成功执行了多个 0 Gas 费交易，无需持有任何 ETH！");
}

// 高级使用示例
async function advancedExample() {
    console.log("\n🔬 高级使用示例");
    console.log("================");

    const config = {
        rpcUrl: 'http://localhost:8545',
        relayerUrl: 'http://localhost:3000',
        contractAddress: '0x...', // 从部署配置中获取
        chainId: 1337
    };

    const sdk = new ZeroGasSDK(config);
    await sdk.initialize(contractABI);

    // 创建多个用户
    const users = [];
    for (let i = 0; i < 3; i++) {
        const wallet = ethers.Wallet.createRandom();
        const provider = new ethers.providers.JsonRpcProvider(config.rpcUrl);
        const signer = wallet.connect(provider);
        users.push({ wallet, signer });
    }

    console.log(`👥 创建了 ${users.length} 个用户`);

    // 为每个用户执行操作
    for (let i = 0; i < users.length; i++) {
        const user = users[i];
        sdk.setSigner(user.signer);

        console.log(`\n👤 用户 ${i + 1}: ${user.wallet.address}`);

        try {
            // 更新资料
            await sdk.executeZeroGasTransaction('updateProfile', [`User${i + 1}`, (i + 1) * 100]);
            console.log(`✅ 用户 ${i + 1} 资料更新成功`);

            // 检查剩余交易次数
            const remaining = await sdk.getRemainingTransactions(user.wallet.address);
            console.log(`📊 剩余免费交易次数: ${remaining}`);

        } catch (error) {
            console.error(`❌ 用户 ${i + 1} 操作失败:`, error.message);
        }

        // 添加延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log("\n🎯 高级示例完成！");
}

// 错误处理和监控示例
async function monitoringExample() {
    console.log("\n📊 监控和错误处理示例");
    console.log("========================");

    const config = {
        rpcUrl: 'http://localhost:8545',
        relayerUrl: 'http://localhost:3000',
        contractAddress: '0x...',
        chainId: 1337
    };

    const sdk = new ZeroGasSDK(config);
    await sdk.initialize(contractABI);

    const userWallet = ethers.Wallet.createRandom();
    const provider = new ethers.providers.JsonRpcProvider(config.rpcUrl);
    const signer = userWallet.connect(provider);
    sdk.setSigner(signer);

    // 监控交易状态
    async function monitorTransaction(txHash) {
        console.log(`🔍 监控交易: ${txHash}`);
        
        try {
            const receipt = await sdk.waitForTransaction(txHash, 30000); // 30秒超时
            console.log(`✅ 交易成功: Gas 使用量 ${receipt.gasUsed}`);
            return receipt;
        } catch (error) {
            console.error(`❌ 交易失败或超时: ${error.message}`);
            return null;
        }
    }

    // 重试机制
    async function executeWithRetry(functionName, params, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 尝试 ${attempt}/${maxRetries}: ${functionName}`);
                
                const result = await sdk.executeZeroGasTransaction(functionName, params);
                console.log(`✅ 第 ${attempt} 次尝试成功`);
                
                return result;
            } catch (error) {
                console.error(`❌ 第 ${attempt} 次尝试失败: ${error.message}`);
                
                if (attempt === maxRetries) {
                    throw new Error(`所有 ${maxRetries} 次尝试都失败了`);
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            }
        }
    }

    // 执行带重试的交易
    try {
        const result = await executeWithRetry('updateProfile', ['Monitored User', 500]);
        
        if (result.transactionHash) {
            await monitorTransaction(result.transactionHash);
        }
    } catch (error) {
        console.error("❌ 最终失败:", error.message);
    }

    console.log("\n📈 监控示例完成！");
}

// 运行示例
if (require.main === module) {
    main()
        .then(() => advancedExample())
        .then(() => monitoringExample())
        .catch(console.error);
}

module.exports = {
    main,
    advancedExample,
    monitoringExample
};
