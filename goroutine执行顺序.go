package main

import (
	"fmt"
	"sync"
	"time"
)

func worker(id int, wg *sync.WaitGroup) {
	defer wg.Done()
	for i := 0; i < 3; i++ {
		fmt.Printf("worker : %d step : %d \n", id, i)
		time.Sleep(time.Millisecond * 100) // 模拟一些工作耗时
	}
}

func main01() {
	var wg sync.WaitGroup
	workers := 5
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go worker(i, &wg)
	}
	wg.Wait()
	fmt.Println("main goroutine done")
}

// 如何保证顺序
//解决方式一： 加锁（不推荐）

//解决方式二：使用通道进行同步控制

func worker1(id int, ch chan struct{}, wg *sync.WaitGroup) {
	defer wg.Done()
	fmt.Printf("Worker %d waiting for signal\n", id)
	<-ch // 等待信号， 启动 5 个 goroutine，但它们都卡在 <-ch 上
	for i := 0; i < 3; i++ {
		fmt.Printf("worker : %d step : %d \n", id, i)
	}

}

func main02() {
	var wg sync.WaitGroup
	workers := 5
	ch := make(chan struct{})

	for i := 0; i < workers; i++ {
		wg.Add(1)
		go worker1(i, ch, &wg)
	}

	// 按顺序发送信号启动每个worker
	for i := 0; i < workers; i++ {
		ch <- struct{}{}
	}
	close(ch)
	wg.Wait()
	fmt.Println("main goroutine done")
}

// 每次只唤醒一个等待中的 worker goroutine，从而大致实现了顺序启动的效果，但这个顺序是 调度相关的，不是严格的启动顺序控制。

// 严格控制 worker 执行顺序 的版本 —— 比如：串行执行，worker 0 完成后才允许 worker 1 执行，依此类推。

func worker03(id int, start <-chan struct{}, next chan<- struct{}, wg *sync.WaitGroup) {
	defer wg.Done()

	<-start // 等待前一个worker 释放信号 , 主 goroutine 启动所有 worker，但他们都在 <-start 阻塞。

	for i := 0; i < 3; i++ {
		fmt.Printf("worker : %d step : %d \n", id, i)
	}

	if next != nil {
		next <- struct{}{} // 通知下一个worker
	}
}

func main03() {
	var wg sync.WaitGroup
	workers := 5

	chans := make([]chan struct{}, workers)
	for i := 0; i < workers; i++ {
		chans[i] = make(chan struct{})
	}

	for i := 0; i < workers; i++ {
		wg.Add(1)
		var next chan struct{}
		if i < workers-1 {
			next = chans[i+1] // 主 goroutine 向 chans[0] 发送信号，释放 worker 0；
		}
		go worker03(i, chans[i], next, &wg)
	}

	// 启动第一个worker
	chans[0] <- struct{}{}

	wg.Wait()
	fmt.Println("main goroutine done")
}

// 并发执行但“顺序打印”的版本

func worker04(id int, resultCh chan<- string, wg *sync.WaitGroup) {
	defer wg.Done()
	var output string
	for i := 0; i < 3; i++ {
		output += fmt.Sprintf("worker : %d step : %d \n", id, i)
	}
	resultCh <- output
}

func main() {
	var wg sync.WaitGroup
	workers := 5

	// 用于按顺序收集每个worker的输出
	resultChans := make([]chan string, workers)
	for i := 0; i < workers; i++ {
		wg.Add(1)
		resultChans[i] = make(chan string, 1)
		go worker04(i, resultChans[i], &wg)
	}

	wg.Wait()

	// 按顺序打印每个worker的输出
	for i := 0; i < workers; i++ {
		fmt.Println(<-resultChans[i])
	}

	fmt.Println("main goroutine done")
}
