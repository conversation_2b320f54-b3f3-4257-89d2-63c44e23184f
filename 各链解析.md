# 各链解析技术文档

本文档详细介绍了主要区块链的RPC接口、交易解析、手续费计算、NFT处理和智能合约交互的技术实现细节。

## 目录
- [EOS](#eos)
- [Stellar (XLM)](#stellar-xlm)
- [Sui](#sui)
- [TRON](#tron)
- [TON](#ton)

---

# EOS

## 概述
EOS是一个高性能的区块链平台，使用委托权益证明(DPoS)共识机制。EOS提供了丰富的RPC API接口用于与区块链交互。

## 核心RPC接口

### 1. 账户查询接口

#### get_account
**接口地址**: `/v1/chain/get_account`
**方法**: POST
**功能**: 获取账户信息

**请求参数**:
```json
{
  "account_name": "eosio"
}
```

**返回数据**:
```json
{
  "account_name": "eosio",
  "head_block_num": 12345,
  "head_block_time": "2023-01-01T00:00:00.000",
  "privileged": true,
  "last_code_update": "1970-01-01T00:00:00.000",
  "created": "2018-06-01T12:00:00.000",
  "core_liquid_balance": "1000.0000 EOS",
  "ram_quota": **********,
  "net_weight": 10000,
  "cpu_weight": 10000,
  "net_limit": {
    "used": 0,
    "available": 1048576,
    "max": 1048576
  },
  "cpu_limit": {
    "used": 0,
    "available": 200000,
    "max": 200000
  },
  "ram_usage": 2724,
  "permissions": [...]
}
```

#### get_currency_balance
**接口地址**: `/v1/chain/get_currency_balance`
**方法**: POST
**功能**: 获取代币余额

**请求参数**:
```json
{
  "code": "eosio.token",
  "account": "alice",
  "symbol": "EOS"
}
```

### 2. 交易查询接口

#### get_transaction
**接口地址**: `/v1/history/get_transaction`
**方法**: POST
**功能**: 根据交易ID获取交易详情

**请求参数**:
```json
{
  "id": "abcd1234..."
}
```

**返回数据**:
```json
{
  "id": "abcd1234...",
  "trx": {
    "receipt": {
      "status": "executed",
      "cpu_usage_us": 500,
      "net_usage_words": 16
    },
    "trx": {
      "expiration": "2023-01-01T00:05:00",
      "ref_block_num": 12345,
      "ref_block_prefix": ********9,
      "max_net_usage_words": 0,
      "max_cpu_usage_ms": 0,
      "delay_sec": 0,
      "context_free_actions": [],
      "actions": [
        {
          "account": "eosio.token",
          "name": "transfer",
          "authorization": [
            {
              "actor": "alice",
              "permission": "active"
            }
          ],
          "data": {
            "from": "alice",
            "to": "bob",
            "quantity": "10.0000 EOS",
            "memo": "payment"
          }
        }
      ]
    }
  }
}
```

### 3. 充值转账解析

#### 转账交易识别
- **Action类型**: `eosio.token::transfer`
- **关键字段**:
  - `from`: 发送方账户
  - `to`: 接收方账户
  - `quantity`: 转账金额和代币符号
  - `memo`: 转账备注

#### 代码示例
```javascript
// 解析EOS转账交易
function parseEOSTransfer(transaction) {
  const actions = transaction.trx.trx.actions;
  const transfers = actions.filter(action =>
    action.account === 'eosio.token' &&
    action.name === 'transfer'
  );

  return transfers.map(transfer => ({
    from: transfer.data.from,
    to: transfer.data.to,
    amount: transfer.data.quantity,
    memo: transfer.data.memo,
    txId: transaction.id
  }));
}
```

### 4. 手续费计算

EOS的手续费机制基于资源消耗：

#### CPU资源
- **单位**: 微秒(microseconds)
- **计算**: 基于交易执行时间
- **获取方式**: 通过抵押EOS获得

#### NET资源
- **单位**: 字节(bytes)
- **计算**: 基于交易数据大小
- **获取方式**: 通过抵押EOS获得

#### RAM资源
- **单位**: 字节(bytes)
- **计算**: 基于存储数据大小
- **获取方式**: 购买RAM

#### 手续费查询接口
```json
// get_account返回的资源信息
{
  "cpu_limit": {
    "used": 1500,        // 已使用CPU(微秒)
    "available": 198500, // 可用CPU(微秒)
    "max": 200000       // 最大CPU(微秒)
  },
  "net_limit": {
    "used": 256,         // 已使用NET(字节)
    "available": 1048320, // 可用NET(字节)
    "max": 1048576      // 最大NET(字节)
  },
  "ram_usage": 2724,     // 已使用RAM(字节)
  "ram_quota": ********** // RAM配额(字节)
}
```

### 5. NFT处理

EOS上的NFT通常基于`atomicassets`标准：

#### NFT转移接口
**合约**: `atomicassets`
**Action**: `transfer`

**数据结构**:
```json
{
  "from": "alice",
  "to": "bob",
  "asset_ids": ["*************"],
  "memo": "NFT transfer"
}
```

#### NFT查询
使用`get_table_rows`接口查询NFT资产：

```json
{
  "json": true,
  "code": "atomicassets",
  "scope": "alice",
  "table": "assets",
  "limit": 100
}
```

### 6. 智能合约交互

#### 合约调用
**接口地址**: `/v1/chain/push_transaction`
**方法**: POST

**交易结构**:
```json
{
  "signatures": ["SIG_K1_..."],
  "compression": "none",
  "packed_context_free_data": "",
  "packed_trx": "..."
}
```

#### 合约查询
使用`get_table_rows`查询合约状态：

```json
{
  "json": true,
  "code": "contract_name",
  "scope": "scope_name",
  "table": "table_name",
  "lower_bound": "",
  "upper_bound": "",
  "limit": 100,
  "reverse": false
}
```

---

# Stellar (XLM)

## 概述
Stellar是一个专注于跨境支付的区块链网络，使用Stellar Consensus Protocol(SCP)共识机制。Stellar提供Horizon API用于与网络交互。

## 核心API接口

### 1. 账户查询接口

#### 获取账户信息
**接口地址**: `GET /accounts/{account_id}`
**功能**: 获取账户详细信息

**请求示例**:
```
GET https://horizon.stellar.org/accounts/GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3
```

**返回数据**:
```json
{
  "id": "GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3",
  "account_id": "GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3",
  "sequence": "***********",
  "subentry_count": 0,
  "thresholds": {
    "low_threshold": 0,
    "med_threshold": 0,
    "high_threshold": 0
  },
  "flags": {
    "auth_required": false,
    "auth_revocable": false,
    "auth_immutable": false
  },
  "balances": [
    {
      "balance": "9999.9999900",
      "buying_liabilities": "0.0000000",
      "selling_liabilities": "0.0000000",
      "asset_type": "native"
    }
  ],
  "signers": [
    {
      "weight": 1,
      "key": "GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3",
      "type": "ed25519_public_key"
    }
  ]
}
```

### 2. 交易查询接口

#### 获取交易详情
**接口地址**: `GET /transactions/{hash}`
**功能**: 根据交易哈希获取交易信息

**返回数据**:
```json
{
  "id": "5ebd5c0af4385500b53dd63b0ef5f6e8feef1a7e1c436a19a40c18b16d1c9d12",
  "paging_token": "***********",
  "successful": true,
  "hash": "5ebd5c0af4385500b53dd63b0ef5f6e8feef1a7e1c436a19a40c18b16d1c9d12",
  "ledger": 3000,
  "created_at": "2023-01-01T00:00:00Z",
  "source_account": "GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3",
  "source_account_sequence": "***********",
  "fee_account": "GDQNY3PBOJOKYZSRMK2S7LHHGWZIUISD4QORETLMXEWXBI7KFZZMKTL3",
  "fee_charged": "100",
  "max_fee": "100",
  "operation_count": 1,
  "envelope_xdr": "...",
  "result_xdr": "...",
  "result_meta_xdr": "..."
}
```

#### 获取账户交易历史
**接口地址**: `GET /accounts/{account_id}/transactions`
**参数**:
- `cursor`: 分页游标
- `limit`: 返回数量限制(最大200)
- `order`: 排序方式(asc/desc)

### 3. 充值转账解析

#### Payment操作解析
Stellar的转账通过Payment操作实现：

**操作类型**: `payment`
**关键字段**:
- `from`: 发送方账户
- `to`: 接收方账户
- `amount`: 转账金额
- `asset_type`: 资产类型(native为XLM)
- `asset_code`: 资产代码
- `asset_issuer`: 资产发行方

#### 代码示例
```javascript
// 解析Stellar支付操作
function parseStellarPayment(operation) {
  if (operation.type === 'payment') {
    return {
      from: operation.from,
      to: operation.to,
      amount: operation.amount,
      asset: operation.asset_type === 'native' ? 'XLM' :
             `${operation.asset_code}:${operation.asset_issuer}`,
      transactionId: operation.transaction_hash
    };
  }
}

// 获取账户交易历史
async function getAccountTransactions(accountId) {
  const response = await fetch(
    `https://horizon.stellar.org/accounts/${accountId}/transactions?limit=200&order=desc`
  );
  return await response.json();
}
```

### 4. 手续费计算

#### 基础手续费结构
- **基础费用**: 100 stroops per operation (0.00001 XLM)
- **最小费用**: 100 stroops
- **费用单位**: stroops (1 XLM = 10,000,000 stroops)

#### 费用计算公式
```
transaction_fee = operation_count × base_fee
```

#### 动态费用机制
当网络拥堵时，费用可能上涨：
- **费用倍数**: 基于网络负载动态调整
- **最大费用**: 交易中设置的max_fee参数

#### 费用查询接口
**接口地址**: `GET /ledgers/{sequence}/effects`
**功能**: 查询账本中的费用效果

### 5. 资产处理

#### 自定义资产
Stellar支持发行自定义资产：

**资产标识**:
- `asset_code`: 资产代码(如USDC)
- `asset_issuer`: 发行方账户地址

#### 信任线设置
在接收自定义资产前需要建立信任线：

**操作类型**: `change_trust`
**参数**:
```json
{
  "asset": "USDC:GCKFBEIYTKP5RDBQMU2FJBG2Y6CKAEOTTGMTNQB2WQPUTXLI5RCHF3R2",
  "limit": "1000000"
}
```

### 6. 智能合约交互

#### Soroban智能合约
Stellar的新一代智能合约平台：

**合约调用接口**: `POST /transactions`
**操作类型**: `invoke_host_function`

**参数结构**:
```json
{
  "host_function": {
    "type": "invoke_contract",
    "invoke_contract": {
      "contract_address": "CONTRACT_ADDRESS",
      "function_name": "function_name",
      "args": [...]
    }
  }
}
```

# Sui

## 概述
Sui是一个高性能的Layer 1区块链，采用基于对象的数据模型和Move编程语言。Sui提供了完整的JSON-RPC API用于与区块链交互。

## 核心RPC接口

### 1. 网络连接
**主网RPC**: `https://fullnode.mainnet.sui.io:443`
**测试网RPC**: `https://fullnode.testnet.sui.io:443`
**开发网RPC**: `https://fullnode.devnet.sui.io:443`

### 2. 账户和余额查询

#### suix_getAllBalances
**功能**: 获取地址的所有代币余额
**方法**: POST

**请求参数**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "suix_getAllBalances",
  "params": [
    "0x94f1a597b4e8f709a396f7f6b1482bdcd65a673d111e49286c527fab7c2d0961"
  ]
}
```

**返回数据**:
```json
{
  "jsonrpc": "2.0",
  "result": [
    {
      "coinType": "0x2::sui::SUI",
      "coinObjectCount": 15,
      "totalBalance": "3000000000",
      "lockedBalance": {}
    }
  ],
  "id": 1
}
```

#### suix_getBalance
**功能**: 获取特定代币余额
**参数**:
- `owner`: 账户地址
- `coin_type`: 代币类型(可选，默认为SUI)

#### suix_getCoins
**功能**: 获取账户拥有的特定类型代币对象
**支持分页**: 通过cursor和limit参数

### 3. 交易查询接口

#### sui_getTransactionBlock
**功能**: 根据交易摘要获取交易详情

**请求参数**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "sui_getTransactionBlock",
  "params": [
    "oRzrVFVgGsFMgaYhW4HKsJzTVVbUUvULe6KcWFdQFxo",
    {
      "showInput": true,
      "showEffects": true,
      "showEvents": true,
      "showObjectChanges": true,
      "showBalanceChanges": true
    }
  ]
}
```

**返回数据结构**:
```json
{
  "digest": "交易摘要",
  "transaction": {
    "data": {
      "sender": "发送方地址",
      "gasData": {
        "payment": "Gas支付对象",
        "owner": "Gas拥有者",
        "price": "Gas价格",
        "budget": "Gas预算"
      }
    }
  },
  "effects": {
    "status": {"status": "success"},
    "gasUsed": {
      "computationCost": "计算费用",
      "storageCost": "存储费用",
      "storageRebate": "存储退款",
      "nonRefundableStorageFee": "不可退还存储费"
    }
  },
  "balanceChanges": [
    {
      "owner": {"AddressOwner": "地址"},
      "coinType": "代币类型",
      "amount": "变化金额"
    }
  ]
}
```

#### suix_queryTransactionBlocks
**功能**: 根据查询条件获取交易列表
**支持过滤器**:
- `FromAddress`: 发送方地址
- `ToAddress`: 接收方地址
- `InputObject`: 输入对象
- `ChangedObject`: 变更对象

### 4. 充值转账解析

#### SUI转账识别
Sui的转账通过`TransferSui`或`Pay`交易类型实现：

**交易类型**: `TransferSui`
**关键字段**:
- `recipient`: 接收方地址
- `amount`: 转账金额(以MIST为单位，1 SUI = 10^9 MIST)

#### 代码示例
```javascript
// 解析Sui转账交易
function parseSuiTransfer(transactionBlock) {
  const balanceChanges = transactionBlock.balanceChanges || [];
  const transfers = [];

  // 分析余额变化来识别转账
  const suiChanges = balanceChanges.filter(change =>
    change.coinType === '0x2::sui::SUI'
  );

  // 负数为发送，正数为接收
  const sender = suiChanges.find(change =>
    parseInt(change.amount) < 0
  );
  const receiver = suiChanges.find(change =>
    parseInt(change.amount) > 0
  );

  if (sender && receiver) {
    transfers.push({
      from: sender.owner.AddressOwner,
      to: receiver.owner.AddressOwner,
      amount: Math.abs(parseInt(receiver.amount)),
      coinType: 'SUI',
      txDigest: transactionBlock.digest
    });
  }

  return transfers;
}
```

### 5. 手续费计算

#### Gas费用结构
Sui的手续费由以下部分组成：

```
total_gas_fees = computation_units × reference_gas_price + storage_units × storage_price
net_gas_fees = computation_gas_fee + storage_gas_fee - storage_rebate
```

#### Gas价格查询
**接口**: `suix_getReferenceGasPrice`
**功能**: 获取当前参考Gas价格

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "suix_getReferenceGasPrice",
  "params": []
}
```

# TRON

## 概述
TRON是一个高吞吐量的区块链平台，支持智能合约和DApp开发。TRON提供HTTP API、JSON-RPC API和gRPC API多种接口方式。

## 核心API接口

### 1. 网络连接
**主网API**: `https://api.trongrid.io`
**Shasta测试网**: `https://api.shasta.trongrid.io`
**Nile测试网**: `https://nile.trongrid.io`

### 2. 账户查询接口

#### GetAccount
**接口地址**: `POST /wallet/getaccount`
**功能**: 获取账户信息

**请求参数**:
```json
{
  "address": "TRX9Uhjn8yRAj9XSKjjK3DLFAaKMPTkK29",
  "visible": true
}
```

**返回数据**:
```json
{
  "address": "TRX9Uhjn8yRAj9XSKjjK3DLFAaKMPTkK29",
  "balance": **********,
  "create_time": *************,
  "latest_opration_time": *************,
  "free_net_usage": 5000,
  "net_usage": 0,
  "net_limit": 5000,
  "total_net_limit": ***********,
  "total_net_weight": ***********,
  "energy_limit": 0,
  "total_energy_limit": ***********,
  "total_energy_weight": ********,
  "account_resource": {
    "energy_usage": 0,
    "frozen_balance_for_energy": {
      "frozen_balance": 0,
      "expire_time": *************
    },
    "latest_consume_time_for_energy": *************,
    "acquired_delegated_frozen_balance_for_energy": 0,
    "delegated_frozen_balance_for_energy": 0,
    "storage_limit": 0,
    "storage_usage": 0,
    "latest_exchange_storage_time": *************
  }
}
```

#### GetAccountBalance
**接口地址**: `POST /wallet/getaccountbalance`
**功能**: 获取账户TRX余额

### 3. 交易查询接口

#### GetTransactionById
**接口地址**: `POST /wallet/gettransactionbyid`
**功能**: 根据交易ID获取交易详情

**请求参数**:
```json
{
  "value": "d5ec749ecc2a615399d8a6c864ea4c74ff9f523c2be0e341ac9be5d47d7c2d62",
  "visible": true
}
```

**返回数据**:
```json
{
  "ret": [
    {
      "contractRet": "SUCCESS"
    }
  ],
  "signature": ["..."],
  "txID": "d5ec749ecc2a615399d8a6c864ea4c74ff9f523c2be0e341ac9be5d47d7c2d62",
  "raw_data": {
    "contract": [
      {
        "parameter": {
          "value": {
            "amount": 1000000,
            "owner_address": "TRX9Uhjn8yRAj9XSKjjK3DLFAaKMPTkK29",
            "to_address": "TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH"
          },
          "type_url": "type.googleapis.com/protocol.TransferContract"
        },
        "type": "TransferContract"
      }
    ],
    "ref_block_bytes": "ab93",
    "ref_block_hash": "88c6e64c7b2d6c1b",
    "expiration": *************,
    "fee_limit": **********,
    "timestamp": *************
  },
  "raw_data_hex": "..."
}
```

#### GetTransactionInfoById
**接口地址**: `POST /wallet/gettransactioninfobyid`
**功能**: 获取交易执行信息(包括手续费详情)

**返回数据**:
```json
{
  "id": "d5ec749ecc2a615399d8a6c864ea4c74ff9f523c2be0e341ac9be5d47d7c2d62",
  "fee": 1100000,
  "blockNumber": 1000,
  "blockTimeStamp": *************,
  "contractResult": [""],
  "contract_address": "TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH",
  "receipt": {
    "energy_usage": 0,
    "energy_fee": 0,
    "origin_energy_usage": 0,
    "energy_usage_total": 0,
    "net_usage": 268,
    "net_fee": 1000000,
    "result": "SUCCESS"
  },
  "log": []
}
```

### 4. 充值转账解析

#### TRX转账识别
**合约类型**: `TransferContract`
**关键字段**:
- `owner_address`: 发送方地址
- `to_address`: 接收方地址
- `amount`: 转账金额(单位：sun，1 TRX = 10^6 sun)

#### TRC20代币转账
**合约类型**: `TriggerSmartContract`
**合约地址**: TRC20代币合约地址
**方法签名**: `transfer(address,uint256)`

#### 代码示例
```javascript
// 解析TRON转账交易
function parseTronTransfer(transaction) {
  const contracts = transaction.raw_data.contract;
  const transfers = [];

  contracts.forEach(contract => {
    if (contract.type === 'TransferContract') {
      // TRX转账
      const data = contract.parameter.value;
      transfers.push({
        type: 'TRX',
        from: data.owner_address,
        to: data.to_address,
        amount: data.amount,
        txId: transaction.txID
      });
    } else if (contract.type === 'TriggerSmartContract') {
      // 可能是TRC20转账，需要解析data字段
      const data = contract.parameter.value;
      if (data.data && data.data.startsWith('a9059cbb')) {
        // transfer方法签名
        const to = '41' + data.data.substring(32, 72);
        const amount = parseInt(data.data.substring(72, 136), 16);
        transfers.push({
          type: 'TRC20',
          contract: data.contract_address,
          from: data.owner_address,
          to: to,
          amount: amount,
          txId: transaction.txID
        });
      }
    }
  });

  return transfers;
}
```

### 5. 手续费计算

#### 资源消耗机制
TRON使用带宽(Bandwidth)和能量(Energy)资源：

#### 带宽(Bandwidth)
- **用途**: 所有交易都消耗带宽
- **免费额度**: 每个账户每天5000带宽点
- **计算公式**: `交易字节数 × 带宽点数`
- **价格**: 1000 sun per 带宽点(当免费额度用完时)

#### 能量(Energy)
- **用途**: 智能合约执行消耗能量
- **获取方式**: 抵押TRX或直接支付
- **价格**: 动态调整，通过`GetEnergyPrices`接口查询

#### 手续费查询接口

##### GetEnergyPrices
**接口地址**: `GET /wallet/getenergyprices`
**功能**: 获取当前能量价格

**返回数据**:
```json
{
  "prices": "420"  // 单位：sun per energy
}
```

##### GetBandwidthPrices
**接口地址**: `GET /wallet/getbandwidthprices`
**功能**: 获取当前带宽价格

#### 费用计算示例
```javascript
// TRON手续费计算
function calculateTronFee(transaction, energyPrice, bandwidthPrice) {
  const txSize = JSON.stringify(transaction).length;
  const bandwidthCost = txSize * bandwidthPrice;

  // 如果是智能合约交易，还需要计算能量费用
  let energyCost = 0;
  if (transaction.raw_data.contract[0].type === 'TriggerSmartContract') {
    // 需要通过EstimateEnergy接口预估
    energyCost = estimatedEnergy * energyPrice;
  }

  return bandwidthCost + energyCost;
}
```

### 6. TRC20代币处理

#### TRC20转账
**接口地址**: `POST /wallet/triggersmartcontract`
**功能**: 调用TRC20合约转账

**请求参数**:
```json
{
  "owner_address": "TRX9Uhjn8yRAj9XSKjjK3DLFAaKMPTkK29",
  "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
  "function_selector": "transfer(address,uint256)",
  "parameter": "000000000000000000000000389ffce9cd8aba693d8a0a1b8f9ba2e7b8b5a5e***********00000000000000000000000000000000000000000000174876e800",
  "fee_limit": 100000000,
  "call_value": 0,
  "visible": true
}
```

#### TRC20余额查询
**方法**: `balanceOf(address)`
**参数编码**: 地址需要转换为32字节十六进制

# TON

## 概述
TON(The Open Network)是一个高性能的区块链平台，支持分片技术和智能合约。TON提供多种API接口，包括TON Center API、TonAPI等。

## 核心API接口

### 1. 网络连接
**TON Center API**: `https://toncenter.com/api/v2/`
**TonAPI**: `https://tonapi.io/`
**主网RPC**: `https://toncenter.com/api/v2/jsonRPC`

### 2. 账户查询接口

#### getAddressInformation
**接口地址**: `GET /getAddressInformation`
**功能**: 获取地址信息

**请求参数**:
```
GET https://toncenter.com/api/v2/getAddressInformation?address=EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n
```

**返回数据**:
```json
{
  "ok": true,
  "result": {
    "balance": "**********",
    "code": "",
    "data": "",
    "last_transaction_id": {
      "lt": "********",
      "hash": "abcd1234..."
    },
    "block_id": {
      "workchain": 0,
      "shard": "****************",
      "seqno": 12345
    },
    "frozen_hash": "",
    "sync_utime": **********,
    "account_state": "active"
  }
}
```

#### getWalletInformation
**功能**: 获取钱包详细信息
**返回**: 包括余额、交易历史等

### 3. 交易查询接口

#### getTransactions
**接口地址**: `GET /getTransactions`
**功能**: 获取地址的交易历史

**请求参数**:
```
GET https://toncenter.com/api/v2/getTransactions?address=EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n&limit=10&to_lt=0&archival=false
```

**返回数据**:
```json
{
  "ok": true,
  "result": [
    {
      "utime": **********,
      "data": "te6cc...",
      "transaction_id": {
        "lt": "********",
        "hash": "abcd1234..."
      },
      "fee": "1000000",
      "storage_fee": "100000",
      "other_fee": "900000",
      "in_msg": {
        "source": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
        "destination": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
        "value": "**********",
        "fwd_fee": "666672",
        "ihr_fee": "0",
        "created_lt": "********",
        "body_hash": "abcd1234...",
        "msg_data": {
          "text": "Hello",
          "body": "te6cc..."
        },
        "message": "Hello"
      },
      "out_msgs": []
    }
  ]
}
```

### 4. 充值转账解析

#### TON转账识别
TON的转账通过内部消息实现：

**关键字段**:
- `source`: 发送方地址
- `destination`: 接收方地址
- `value`: 转账金额(单位：nanoton，1 TON = 10^9 nanoton)
- `message`: 转账备注

#### Jetton代币转账
Jetton是TON上的代币标准，类似于ERC20：

**转账消息结构**:
```json
{
  "op": "0x0f8a7ea5",  // Jetton transfer操作码
  "query_id": "0",
  "amount": "**********",
  "destination": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
  "response_destination": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
  "custom_payload": null,
  "forward_ton_amount": "1",
  "forward_payload": ""
}
```

#### 代码示例
```javascript
// 解析TON转账交易
function parseTonTransfer(transaction) {
  const transfers = [];

  // 检查入账消息
  if (transaction.in_msg && transaction.in_msg.value > 0) {
    transfers.push({
      type: 'TON',
      from: transaction.in_msg.source,
      to: transaction.in_msg.destination,
      amount: transaction.in_msg.value,
      message: transaction.in_msg.message,
      txHash: transaction.transaction_id.hash
    });
  }

  // 检查出账消息
  transaction.out_msgs.forEach(msg => {
    if (msg.value > 0) {
      transfers.push({
        type: 'TON',
        from: msg.source,
        to: msg.destination,
        amount: msg.value,
        message: msg.message,
        txHash: transaction.transaction_id.hash
      });
    }
  });

  return transfers;
}

// 解析Jetton转账
function parseJettonTransfer(msgBody) {
  // 解析消息体中的Jetton转账操作
  if (msgBody.startsWith('0x0f8a7ea5')) {
    return {
      op: 'jetton_transfer',
      queryId: msgBody.substring(8, 24),
      amount: parseInt(msgBody.substring(24, 88), 16),
      destination: msgBody.substring(88, 152),
      // ... 其他字段解析
    };
  }
}
```

### 5. 手续费计算

#### 费用组成
TON的交易费用由以下部分组成：

```
transaction_fee = storage_fees + in_fwd_fees + computation_fees + action_fees + out_fwd_fees
```

#### 各项费用说明
- **storage_fees**: 存储费用，按时间计算
- **in_fwd_fees**: 外部消息导入费用
- **computation_fees**: 计算执行费用
- **action_fees**: 发送消息费用
- **out_fwd_fees**: 外部消息转发费用

#### Gas价格设置
- **基链Gas价格**: 400 nanoton per gas unit
- **主链Gas价格**: 10,000 nanoton per gas unit
- **最小交易费用**: 约0.0025 TON

#### 典型交易费用
- **TON转账**: 约0.0055 TON
- **Jetton转账**: 约0.037 TON
- **NFT铸造**: 约0.08 TON

#### 费用计算示例
```javascript
// TON手续费计算
function calculateTonFee(gasUsed, storageUsed, isMainchain = false) {
  const gasPrice = isMainchain ? 10000 : 400; // nanoton per gas
  const computationFee = gasUsed * gasPrice;
  const storageFee = storageUsed * 1000; // 假设存储价格

  return computationFee + storageFee;
}
```

### 6. NFT处理

#### TON NFT标准
TON使用TEP-62标准定义NFT：

**NFT集合合约**: 管理NFT系列
**NFT项目合约**: 单个NFT实例

#### NFT转移
**操作码**: `0x5fcc3d14`
**消息结构**:
```json
{
  "op": "0x5fcc3d14",
  "query_id": "0",
  "new_owner": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
  "response_destination": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
  "custom_payload": null,
  "forward_amount": "1000000",
  "forward_payload": ""
}
```

#### NFT查询
通过`getAddressInformation`查询NFT合约状态，或使用TON Index API：

```javascript
// 查询地址拥有的NFT
async function getNFTs(address) {
  const response = await fetch(
    `https://toncenter.com/api/v3/nft/items?owner_address=${address}`
  );
  return await response.json();
}
```

### 7. 智能合约交互

#### 合约调用
TON智能合约通过发送内部消息调用：

**消息类型**: `internal`
**调用方式**: 发送特定格式的消息体

#### 合约部署
**接口**: `sendBoc`
**功能**: 发送序列化的交易数据

#### 合约状态查询
**方法**: `runGetMethod`
**功能**: 调用合约的get方法

**请求参数**:
```json
{
  "address": "EQD2NmD_lH5f5u1Kj3KfGyTvhZSX0Eg6qp2a5IQUKXxOG21n",
  "method": "get_balance",
  "stack": []
}
```

## 各链特性对比

| 特性 | EOS | Stellar (XLM) | Sui | TRON | TON |
|------|-----|---------------|-----|------|-----|
| **共识机制** | DPoS | SCP | Proof of Stake | DPoS | Proof of Stake |
| **TPS** | 3,000+ | 1,000+ | 120,000+ | 2,000+ | 1,000,000+ |
| **编程语言** | C++ | - | Move | Solidity | FunC/Tact |
| **智能合约** | EOSIO | Soroban | Move合约 | TVM | TVM |
| **代币标准** | - | 原生资产 | Coin标准 | TRC10/TRC20 | Jetton |
| **NFT标准** | AtomicAssets | - | Object模型 | TRC721 | TEP-62 |
| **手续费模式** | 资源抵押 | 固定费用 | Gas机制 | 带宽+能量 | Gas机制 |
| **地址格式** | 12字符名称 | 56字符公钥 | 0x前缀 | T开头 | EQ开头 |

## 最佳实践

### 1. 交易监控
- **轮询频率**: 建议每5-10秒查询一次新交易
- **确认数**: 根据链的特性设置合适的确认数
- **错误处理**: 实现重试机制和异常处理

### 2. 地址验证
```javascript
// 各链地址验证示例
function validateAddress(address, chain) {
  switch(chain) {
    case 'EOS':
      return /^[a-z1-5.]{1,12}$/.test(address);
    case 'XLM':
      return /^G[A-Z2-7]{55}$/.test(address);
    case 'SUI':
      return /^0x[a-fA-F0-9]{64}$/.test(address);
    case 'TRON':
      return /^T[A-Za-z0-9]{33}$/.test(address);
    case 'TON':
      return /^EQ[A-Za-z0-9_-]{46}$/.test(address);
    default:
      return false;
  }
}
```

### 3. 金额处理
```javascript
// 各链金额单位转换
const DECIMALS = {
  EOS: 4,      // 1 EOS = 10^4 最小单位
  XLM: 7,      // 1 XLM = 10^7 stroops
  SUI: 9,      // 1 SUI = 10^9 MIST
  TRX: 6,      // 1 TRX = 10^6 sun
  TON: 9       // 1 TON = 10^9 nanoton
};

function formatAmount(amount, chain) {
  const decimals = DECIMALS[chain];
  return (amount / Math.pow(10, decimals)).toFixed(decimals);
}
```

### 4. 错误处理
```javascript
// 统一错误处理
async function safeApiCall(apiCall, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      console.log(`API调用失败，重试 ${i + 1}/${retries}:`, error.message);
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 5. 安全建议
- **API密钥管理**: 妥善保管API密钥，避免泄露
- **请求限制**: 遵守各API的请求频率限制
- **数据验证**: 对API返回的数据进行验证
- **网络安全**: 使用HTTPS连接，验证SSL证书

## 开发工具推荐

### 1. 区块链浏览器
- **EOS**: [EOS Explorer](https://eospark.com/)
- **Stellar**: [Stellar Expert](https://stellar.expert/)
- **Sui**: [Sui Explorer](https://suiexplorer.com/)
- **TRON**: [TRONSCAN](https://tronscan.org/)
- **TON**: [TON Explorer](https://tonviewer.com/)

### 2. 开发SDK
- **EOS**: eosjs, eosio-java
- **Stellar**: js-stellar-sdk, py-stellar-base
- **Sui**: @mysten/sui.js, sui-sdk-python
- **TRON**: tronweb, tron-grid
- **TON**: ton-core, tonweb

### 3. 测试网络
- **EOS**: Jungle Testnet
- **Stellar**: Testnet
- **Sui**: Devnet/Testnet
- **TRON**: Shasta/Nile Testnet
- **TON**: Testnet

## 注意事项

### 1. API限制
- 大多数公共API都有请求频率限制
- 生产环境建议使用付费API服务
- 实现适当的缓存机制

### 2. 数据一致性
- 不同API提供商可能存在数据延迟
- 关键操作建议多源验证
- 注意处理链重组情况

### 3. 版本兼容性
- 各链的API可能会升级更新
- 及时关注官方公告和变更
- 实现向后兼容的代码结构

---

*最后更新时间: 2025年8月*
*文档版本: v1.0*

> **免责声明**: 本文档仅供技术参考，实际开发时请以各区块链官方文档为准。API接口和参数可能随版本更新而变化。
