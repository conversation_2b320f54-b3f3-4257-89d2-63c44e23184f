# 网络配置
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_PROJECT_ID
GOERLI_RPC_URL=https://goerli.infura.io/v3/YOUR_PROJECT_ID
POLYGON_RPC_URL=https://polygon-rpc.com
MUMBAI_RPC_URL=https://rpc-mumbai.maticvigil.com

# 私钥 (用于部署和 Relayer)
PRIVATE_KEY=your_private_key_here

# API 密钥
ETHERSCAN_API_KEY=your_etherscan_api_key
POLYGONSCAN_API_KEY=your_polygonscan_api_key

# Gas 报告
REPORT_GAS=true

# Relayer 配置
RELAYER_PORT=3000
RELAYER_ENDPOINT=http://localhost:3000
CHAIN_ID=1337
ALLOWED_ORIGINS=http://localhost:3000,https://yourapp.com

# 合约地址 (部署后填写)
ZEROGAS_CONTRACT_ADDRESS=
RELAYER_SERVICE_CONTRACT_ADDRESS=
