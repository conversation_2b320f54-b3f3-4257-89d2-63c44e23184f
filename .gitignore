# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Hardhat files
cache/
artifacts/
typechain-types/

# Coverage reports
coverage/
coverage.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Deployment artifacts
deployment-config.json
deployments/

# Temporary files
tmp/
temp/

# Go files (not related to this project)
*.go
go.mod
go.sum

# Markdown files (interview materials, not related to this project)
*面试题*.md
*面试题*.go
交易解析*.md
共识算法.md
各链解析.md
okx.md

# Build outputs
dist/
build/
