package main

import (
	"fmt"
	"sync"
	"time"
)

func fetchData(id int) {
	time.Sleep(12 * time.Microsecond) // 模拟请求延迟
	fmt.Println("fetch data %d", id)
}

func main01() {
	var wg sync.WaitGroup
	totalRequests := 1000 // 大量请求

	for i := 0; i < totalRequests; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			fetchData(id)
		}(i)
	}
	wg.Wait()
	fmt.Println("main goroutine done")
}

/*
在这个示例中：
	- main 函数启动了 10,000 个 goroutine，每个 goroutine 并发地执行 fetchData。
	- 由于 goroutine 数量过多，系统的内存和 CPU 资源将被大量消耗，可能导致性能下降甚至崩溃。
运行风险
	- 内存消耗过大：每个 goroutine 都占用内存，大量 goroutine 可能导致内存溢出。
	- CPU 资源争用：过多的 goroutine 会竞争 CPU 资源，降低处理效率。
	- 频繁的垃圾回收：大量 goroutine 会生成许多临时变量，触发 GC 更频繁地运行，从而增加开销。
*/

// 解决方案：
// 方式一： 使用Goroutine 池

func main() {
	var wg sync.WaitGroup
	totalRequests := 1000
	maxConcurrency := 100 // 控制最大并发数

	// 创建一个带缓冲的通道作为 goroutine 池
	pool := make(chan struct{}, maxConcurrency)

	for i := 0; i < totalRequests; i++ {
		wg.Add(1)

		// 向goroutine池添加一个任务（如果池满也会阻塞）
		pool <- struct{}{}

		go func(id int) {
			defer wg.Done()
			fetchData(id)
			<-pool // 完成任务后从池中移除
		}(i)
	}
	wg.Wait()
	fmt.Println("main goroutine done")
}
